<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator</title>
</head>
<body>
    <canvas id="canvas" style="border: 1px solid black;"></canvas>
    <br><br>
    <button onclick="generateIcons()">Generate Icons</button>
    <div id="downloads"></div>

    <script>
        const svgData = `<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'>
            <rect width="100" height="100" rx="20" ry="20" fill="white"/>
            <path fill='#a78bfa' d='M50,25.2C30.4,25.2,23.5,40.7,24.5,52.7c1,11,10.2,27.1,25.5,27.1s24.5-16.1,25.5-27.1C76.5,40.7,69.6,25.2,50,25.2z'/>
            <circle cx='44' cy='55' r='4' fill='white'/>
            <circle cx='56' cy='55' r='4' fill='white'/>
        </svg>`;

        const iconSizes = [
            { name: 'ldpi', size: 36 },
            { name: 'mdpi', size: 48 },
            { name: 'hdpi', size: 72 },
            { name: 'xhdpi', size: 96 },
            { name: 'xxhdpi', size: 144 },
            { name: 'xxxhdpi', size: 192 }
        ];

        function generateIcons() {
            const downloadsDiv = document.getElementById('downloads');
            downloadsDiv.innerHTML = '';

            iconSizes.forEach(iconSize => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = iconSize.size;
                canvas.height = iconSize.size;

                const img = new Image();
                const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
                const url = URL.createObjectURL(svgBlob);

                img.onload = function() {
                    ctx.drawImage(img, 0, 0, iconSize.size, iconSize.size);
                    
                    canvas.toBlob(function(blob) {
                        const link = document.createElement('a');
                        link.download = `icon-${iconSize.size}-${iconSize.name}.png`;
                        link.href = URL.createObjectURL(blob);
                        link.textContent = `Download ${iconSize.name} (${iconSize.size}x${iconSize.size})`;
                        link.style.display = 'block';
                        link.style.margin = '5px 0';
                        downloadsDiv.appendChild(link);
                        
                        // Auto-click to download
                        link.click();
                    });
                    
                    URL.revokeObjectURL(url);
                };
                
                img.src = url;
            });
        }
    </script>
</body>
</html>
