<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الدردشة - العقل المبدع</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .glassmorphism {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .expert-card {
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .expert-card:hover {
            border-color: rgba(147, 51, 234, 0.5);
            box-shadow: 0 8px 32px rgba(147, 51, 234, 0.2);
            transform: scale(1.05);
        }

        .messages-container {
            max-height: 400px;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(147, 51, 234, 0.5) transparent;
        }

        .message {
            animation: messageSlideIn 0.3s ease-out;
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body class="text-white">
    <div class="container mx-auto p-4 max-w-4xl">
        <h1 class="text-3xl font-bold text-center mb-8">العقل المبدع - دردشة ذكية v3.0</h1>
        
        <!-- قائمة الخبراء -->
        <div id="expertSelector" class="glassmorphism shadow-2xl p-6 md:p-8 mb-8 rounded-2xl">
            <h2 class="text-xl font-bold mb-4 text-center text-purple-300">اختر خبيرك المتخصص</h2>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                <!-- سيتم ملؤها بالجافاسكريبت -->
            </div>
        </div>

        <!-- منطقة الدردشة -->
        <div id="chatContainer" class="glassmorphism shadow-2xl p-6 md:p-8 mb-8 rounded-2xl hidden">
            <!-- معلومات الخبير -->
            <div class="flex items-center gap-4 mb-6 pb-4 border-b border-gray-700/30">
                <div id="expertAvatar" class="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-lg">
                    📚
                </div>
                <div>
                    <h3 id="expertName" class="font-bold text-lg">أستاذ سرد</h3>
                    <p id="expertRole" class="text-sm text-gray-400">خبير في فنون الكتابة والسرد</p>
                </div>
                <button onclick="backToExperts()" class="mr-auto p-2 hover:bg-gray-700/30 rounded-lg transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"/>
                    </svg>
                </button>
            </div>

            <!-- منطقة الرسائل -->
            <div id="messagesContainer" class="messages-container mb-4 p-4 bg-gray-900/20 rounded-lg border border-gray-700/30">
                <!-- الرسائل ستظهر هنا -->
            </div>

            <!-- منطقة الكتابة -->
            <div class="flex gap-3">
                <textarea id="messageInput" placeholder="اكتب رسالتك هنا..." class="flex-1 p-3 bg-gray-900/30 backdrop-filter backdrop-blur-md border-2 border-gray-700/40 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-white placeholder-gray-400 resize-none" rows="1"></textarea>
                <button id="sendButton" class="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105">
                    إرسال
                </button>
            </div>
        </div>

        <!-- الاقتراحات السريعة -->
        <div id="quickSuggestions" class="glassmorphism shadow-2xl p-6 md:p-8 mb-8 rounded-2xl hidden">
            <h3 class="text-lg font-bold mb-4 text-center text-purple-300">اقتراحات سريعة</h3>
            <div id="suggestionButtons" class="grid grid-cols-1 md:grid-cols-2 gap-3"></div>
        </div>
    </div>

    <script>
        // بيانات الخبراء (نسخة مبسطة للاختبار)
        const experts = {
            story: {
                name: "أستاذ سرد",
                role: "خبير في فنون الكتابة والسرد",
                avatar: "📚",
                color: "from-pink-500 to-rose-500",
                suggestions: [
                    "كيف أكتب بداية مشوقة لقصتي؟",
                    "ما هي عناصر القصة الناجحة؟",
                    "كيف أطور شخصيات مقنعة؟",
                    "أريد كتابة قصة خيال علمي"
                ]
            },
            kitchen: {
                name: "الشيف محمد",
                role: "خبير الطبخ والمأكولات العربية",
                avatar: "👨‍🍳",
                color: "from-orange-500 to-red-500",
                suggestions: [
                    "أريد وصفة كبسة دجاج لذيذة",
                    "كيف أحضر معكرونة بالصلصة؟",
                    "وصفات صحية للإفطار",
                    "أطباق سريعة للعشاء"
                ]
            },
            health: {
                name: "د. سارة",
                role: "طبيبة وخبيرة تغذية",
                avatar: "👩‍⚕️",
                color: "from-green-500 to-emerald-500",
                suggestions: [
                    "كيف أتخلص من الصداع طبيعياً؟",
                    "نصائح لتقوية المناعة",
                    "علاج طبيعي للأرق",
                    "كيف أحافظ على وزن صحي؟"
                ]
            }
        };

        let currentExpert = null;
        let chatHistory = [];

        // عرض الخبراء
        function renderExperts() {
            const expertSelector = document.querySelector('#expertSelector .grid');
            expertSelector.innerHTML = Object.keys(experts).map(key => {
                const expert = experts[key];
                return `
                    <div onclick="selectExpert('${key}')" class="expert-card glassmorphism p-4 rounded-xl cursor-pointer text-center">
                        <div class="w-16 h-16 mx-auto mb-3 rounded-full bg-gradient-to-r ${expert.color} flex items-center justify-center text-2xl">
                            ${expert.avatar}
                        </div>
                        <h3 class="font-bold text-white mb-1">${expert.name}</h3>
                        <p class="text-xs text-gray-400">${expert.role}</p>
                    </div>
                `;
            }).join('');
        }

        // اختيار خبير
        function selectExpert(expertKey) {
            currentExpert = experts[expertKey];
            chatHistory = [];
            
            document.getElementById('expertSelector').style.display = 'none';
            document.getElementById('chatContainer').classList.remove('hidden');
            document.getElementById('quickSuggestions').classList.remove('hidden');
            
            document.getElementById('expertAvatar').textContent = currentExpert.avatar;
            document.getElementById('expertAvatar').className = `w-12 h-12 rounded-full bg-gradient-to-r ${currentExpert.color} flex items-center justify-center text-white font-bold text-lg`;
            document.getElementById('expertName').textContent = currentExpert.name;
            document.getElementById('expertRole').textContent = currentExpert.role;
            
            addMessage('expert', `مرحباً! أنا ${currentExpert.name}، ${currentExpert.role}. كيف يمكنني مساعدتك اليوم؟`);
            renderQuickSuggestions();
        }

        // عرض الاقتراحات السريعة
        function renderQuickSuggestions() {
            const suggestionButtons = document.getElementById('suggestionButtons');
            suggestionButtons.innerHTML = currentExpert.suggestions.map(suggestion => `
                <button onclick="sendQuickMessage('${suggestion}')" class="p-3 text-sm bg-gray-800/50 hover:bg-purple-600/30 rounded-lg transition-colors text-right">
                    ${suggestion}
                </button>
            `).join('');
        }

        // العودة للخبراء
        function backToExperts() {
            document.getElementById('expertSelector').style.display = 'block';
            document.getElementById('chatContainer').classList.add('hidden');
            document.getElementById('quickSuggestions').classList.add('hidden');
            currentExpert = null;
            chatHistory = [];
            document.getElementById('messagesContainer').innerHTML = '';
        }

        // إضافة رسالة
        function addMessage(sender, content) {
            const messagesContainer = document.getElementById('messagesContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message mb-4`;
            
            if (sender === 'user') {
                messageDiv.innerHTML = `
                    <div class="flex justify-end">
                        <div class="bg-purple-600 text-white p-3 rounded-lg max-w-xs lg:max-w-md text-right">
                            ${content}
                        </div>
                    </div>
                `;
            } else {
                messageDiv.innerHTML = `
                    <div class="flex items-start gap-3">
                        <div class="w-8 h-8 rounded-full bg-gradient-to-r ${currentExpert.color} flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                            ${currentExpert.avatar}
                        </div>
                        <div class="bg-gray-800/50 text-white p-3 rounded-lg max-w-xs lg:max-w-md text-right">
                            ${content}
                        </div>
                    </div>
                `;
            }
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            chatHistory.push({ sender, content, timestamp: new Date().toISOString() });
        }

        // إرسال رسالة
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || !currentExpert) return;
            
            addMessage('user', message);
            input.value = '';
            
            // محاكاة رد الخبير
            setTimeout(() => {
                const responses = [
                    `شكراً لك على سؤالك حول "${message}". هذا موضوع مهم جداً.`,
                    `بناءً على خبرتي في هذا المجال، أنصحك بالتالي...`,
                    `هذا سؤال ممتاز! دعني أشرح لك بالتفصيل...`,
                    `من واقع تجربتي، يمكنني أن أقول أن...`
                ];
                const randomResponse = responses[Math.floor(Math.random() * responses.length)];
                addMessage('expert', randomResponse);
            }, 1000);
        }

        // إرسال رسالة سريعة
        function sendQuickMessage(message) {
            if (!currentExpert) return;
            addMessage('user', message);
            setTimeout(() => {
                addMessage('expert', `شكراً لاختيارك هذا السؤال. سأقدم لك إجابة شاملة حول "${message}".`);
            }, 1000);
        }

        // إعداد مستمعات الأحداث
        document.addEventListener('DOMContentLoaded', () => {
            renderExperts();
            
            const messageInput = document.getElementById('messageInput');
            const sendButton = document.getElementById('sendButton');
            
            messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
            
            sendButton.addEventListener('click', sendMessage);
        });
    </script>
</body>
</html>
