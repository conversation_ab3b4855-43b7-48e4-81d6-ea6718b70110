<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <script type='text/javascript'>
    // إزالة ?m=1 أو &m=1 من الرابط بدون إعادة تحميل الصفحة وبشكل نظيف
    (function() {
      var url = window.location.href;
      // حذف جميع حالات m=1 من الاستعلام فقط (دون التأثير على الهاش)
      var parts = url.split('#');
      var base = parts[0];
      var hash = parts[1] ? '#' + parts[1] : '';
      // معالجة الاستعلام فقط
      var qIndex = base.indexOf('?');
      if (qIndex !== -1) {
        var path = base.substring(0, qIndex);
        var query = base.substring(qIndex + 1)
          .replace(/(^|&|\?)m=1(&|$)/g, function(match, p1, p2) {
            if ((p1 === '?' || p1 === '') && p2 === '&') return '';
            if (p1 === '&' && p2 === '&') return '&';
            return '';
          })
          .replace(/^&+|&+$/g, '') // إزالة & الزائدة من البداية والنهاية
          .replace(/&&+/g, '&'); // دمج & المتكررة
        var clean = path + (query ? '?' + query : '') + hash;
        if (clean !== url) {
          window.history.replaceState({}, document.title, clean);
        }
      }
    })();
    </script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>العقل المبدع - دردشة ذكية v3.0</title>
    <link rel="icon" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cpath fill='%23a78bfa' d='M50,15.2C25.4,15.2,16.5,35.7,18,50.7c1.4,14,13.2,34.1,32,34.1s30.6-20.1,32-34.1C83.5,35.7,74.6,15.2,50,15.2z'/%3E%3Ccircle cx='42' cy='55' r='5' fill='white'/%3E%3Ccircle cx='58' cy='55' r='5' fill='white'/%3E%3C/svg%3E">

    <!-- PWA Manifest -->
    <link rel="manifest" href="pwa-manifest.json">
    <meta name="theme-color" content="#c039ff">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="العقل المبدع">
    <link rel="apple-touch-icon" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cpath fill='%23a78bfa' d='M50,15.2C25.4,15.2,16.5,35.7,18,50.7c1.4,14,13.2,34.1,32,34.1s30.6-20.1,32-34.1C83.5,35.7,74.6,15.2,50,15.2z'/%3E%3Ccircle cx='42' cy='55' r='5' fill='white'/%3E%3Ccircle cx='58' cy='55' r='5' fill='white'/%3E%3C/svg%3E">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Vazirmatn:wght@400;500;600;700&display=swap');
        
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Vazirmatn', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: #fff;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background:
                radial-gradient(circle at 20% 30%, rgba(147, 197, 253, 0.15) 0%, transparent 70%),
                radial-gradient(circle at 80% 70%, rgba(196, 181, 253, 0.12) 0%, transparent 70%),
                radial-gradient(circle at 50% 50%, rgba(252, 211, 77, 0.08) 0%, transparent 80%),
                radial-gradient(circle at 30% 80%, rgba(167, 243, 208, 0.1) 0%, transparent 75%),
                radial-gradient(circle at 70% 20%, rgba(251, 191, 36, 0.06) 0%, transparent 85%);
            z-index: -1;
            pointer-events: none;
            animation: backgroundShift 20s ease-in-out infinite;
            will-change: background;
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
        }

        @keyframes backgroundShift {
            0%, 100% {
                background:
                    radial-gradient(circle at 20% 30%, rgba(147, 197, 253, 0.15) 0%, transparent 70%),
                    radial-gradient(circle at 80% 70%, rgba(196, 181, 253, 0.12) 0%, transparent 70%),
                    radial-gradient(circle at 50% 50%, rgba(252, 211, 77, 0.08) 0%, transparent 80%),
                    radial-gradient(circle at 30% 80%, rgba(167, 243, 208, 0.1) 0%, transparent 75%),
                    radial-gradient(circle at 70% 20%, rgba(251, 191, 36, 0.06) 0%, transparent 85%);
            }
            25% {
                background:
                    radial-gradient(circle at 80% 20%, rgba(196, 181, 253, 0.12) 0%, transparent 70%),
                    radial-gradient(circle at 20% 80%, rgba(167, 243, 208, 0.15) 0%, transparent 70%),
                    radial-gradient(circle at 60% 40%, rgba(147, 197, 253, 0.1) 0%, transparent 75%),
                    radial-gradient(circle at 30% 70%, rgba(252, 211, 77, 0.08) 0%, transparent 80%),
                    radial-gradient(circle at 70% 30%, rgba(251, 191, 36, 0.06) 0%, transparent 85%);
            }
            50% {
                background:
                    radial-gradient(circle at 60% 80%, rgba(252, 211, 77, 0.12) 0%, transparent 70%),
                    radial-gradient(circle at 40% 20%, rgba(147, 197, 253, 0.15) 0%, transparent 70%),
                    radial-gradient(circle at 80% 60%, rgba(167, 243, 208, 0.1) 0%, transparent 75%),
                    radial-gradient(circle at 20% 40%, rgba(196, 181, 253, 0.08) 0%, transparent 80%),
                    radial-gradient(circle at 50% 80%, rgba(251, 191, 36, 0.06) 0%, transparent 85%);
            }
            75% {
                background:
                    radial-gradient(circle at 30% 50%, rgba(167, 243, 208, 0.12) 0%, transparent 70%),
                    radial-gradient(circle at 70% 50%, rgba(252, 211, 77, 0.15) 0%, transparent 70%),
                    radial-gradient(circle at 50% 20%, rgba(196, 181, 253, 0.1) 0%, transparent 75%),
                    radial-gradient(circle at 80% 80%, rgba(147, 197, 253, 0.08) 0%, transparent 80%),
                    radial-gradient(circle at 20% 20%, rgba(251, 191, 36, 0.06) 0%, transparent 85%);
            }
        }

        @media (max-width: 768px) {
            body::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background:
                    radial-gradient(circle at 20% 30%, rgba(192, 57, 255, 0.6) 0%, transparent 60%),
                    radial-gradient(circle at 80% 70%, rgba(106, 17, 203, 0.7) 0%, transparent 60%),
                    radial-gradient(circle at 50% 50%, rgba(88, 24, 69, 0.5) 0%, transparent 70%),
                    radial-gradient(circle at 40% 60%, rgba(88, 24, 69, 0.6) 0%, transparent 70%),
                    radial-gradient(circle at 60% 40%, rgba(192, 57, 255, 0.5) 0%, transparent 70%);
                z-index: -1;
                filter: blur(0px);
                animation: mobileBackgroundShift 20s ease-in-out infinite;
                will-change: background;
                transform: translateZ(0);
            }
        }

        /* Mobile Background Animation - ألوان ناعمة */
        @keyframes mobileBackgroundShift {
            0%, 100% {
                background:
                    radial-gradient(circle at 20% 30%, rgba(147, 197, 253, 0.15) 0%, transparent 70%),
                    radial-gradient(circle at 80% 70%, rgba(196, 181, 253, 0.12) 0%, transparent 70%),
                    radial-gradient(circle at 50% 50%, rgba(252, 211, 77, 0.08) 0%, transparent 80%),
                    radial-gradient(circle at 30% 80%, rgba(167, 243, 208, 0.1) 0%, transparent 75%),
                    radial-gradient(circle at 70% 20%, rgba(251, 191, 36, 0.06) 0%, transparent 85%);
            }
            25% {
                background:
                    radial-gradient(circle at 80% 20%, rgba(196, 181, 253, 0.12) 0%, transparent 70%),
                    radial-gradient(circle at 20% 80%, rgba(167, 243, 208, 0.15) 0%, transparent 70%),
                    radial-gradient(circle at 60% 40%, rgba(147, 197, 253, 0.1) 0%, transparent 75%),
                    radial-gradient(circle at 30% 70%, rgba(252, 211, 77, 0.08) 0%, transparent 80%),
                    radial-gradient(circle at 70% 30%, rgba(251, 191, 36, 0.06) 0%, transparent 85%);
            }
            50% {
                background:
                    radial-gradient(circle at 60% 80%, rgba(252, 211, 77, 0.12) 0%, transparent 70%),
                    radial-gradient(circle at 40% 20%, rgba(147, 197, 253, 0.15) 0%, transparent 70%),
                    radial-gradient(circle at 80% 60%, rgba(167, 243, 208, 0.1) 0%, transparent 75%),
                    radial-gradient(circle at 20% 40%, rgba(196, 181, 253, 0.08) 0%, transparent 80%),
                    radial-gradient(circle at 50% 80%, rgba(251, 191, 36, 0.06) 0%, transparent 85%);
            }
            75% {
                background:
                    radial-gradient(circle at 30% 50%, rgba(167, 243, 208, 0.12) 0%, transparent 70%),
                    radial-gradient(circle at 70% 50%, rgba(252, 211, 77, 0.15) 0%, transparent 70%),
                    radial-gradient(circle at 50% 20%, rgba(196, 181, 253, 0.1) 0%, transparent 75%),
                    radial-gradient(circle at 80% 80%, rgba(147, 197, 253, 0.08) 0%, transparent 80%),
                    radial-gradient(circle at 20% 20%, rgba(251, 191, 36, 0.06) 0%, transparent 85%);
            }
        }

        :root { --main-color: #c039ff; --secondary-color: #6a11cb; }
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(-45deg, #02041a, #0d1117, #010409, #02041a);
            background-size: 400% 400%;
            animation: gradientBG 15s ease infinite;
            color: #e0e6f0;
            overflow-y: auto;
            overflow-x: hidden;
        }
        @keyframes gradientBG {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        #particles-js {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 0;
    pointer-events: none;
    opacity: 0.7;
}
        .glassmorphism {
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.03);
    border-radius: 1rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}
        .main-button {
            background: linear-gradient(135deg, var(--main-color), var(--secondary-color));
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(192, 57, 255, 0.3);
            border: none;
            font-weight: 600;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }

        .main-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(192, 57, 255, 0.4);
        }

        .main-button:active {
            transform: translateY(0px);
            box-shadow: 0 2px 10px rgba(192, 57, 255, 0.3);
        }

        /* Auto Suggestions Styles */
        #autoSuggestions {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(192, 57, 255, 0.2);
        }

        .suggestion-item {
            padding: 0.75rem 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            color: #e0e6f0;
        }

        .suggestion-item:hover, .suggestion-item.active {
            background: rgba(192, 57, 255, 0.15);
            color: white;
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .suggestion-category {
            font-size: 0.75rem;
            color: #a0aec0;
            margin-bottom: 0.25rem;
        }

        .suggestion-text {
            font-size: 0.9rem;
        }






        .idea-card {
            border-right: 4px solid var(--main-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, rgba(192, 57, 255, 0.02) 0%, rgba(106, 17, 203, 0.01) 100%);
        }
        .idea-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(192, 57, 255, 0.2);
            border-right-color: #c039ff;
        }
        .idea-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(180deg, #c039ff 0%, #6a11cb 50%, #c039ff 100%);
            border-radius: 0 12px 12px 0;
        }
        @keyframes slide-in-bottom { 0% { transform: translateY(20px); opacity: 0; } 100% { transform: translateY(0); opacity: 1; } }
        .animate-slide-in { animation: slide-in-bottom 0.3s ease both; }

        /* أنماط الدردشة */
        .expert-card {
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .expert-card:hover {
            border-color: rgba(147, 51, 234, 0.5);
            box-shadow: 0 8px 32px rgba(147, 51, 234, 0.2);
        }

        .messages-container {
            max-height: 400px;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(147, 51, 234, 0.5) transparent;
        }

        .messages-container::-webkit-scrollbar {
            width: 6px;
        }

        .messages-container::-webkit-scrollbar-track {
            background: transparent;
        }

        .messages-container::-webkit-scrollbar-thumb {
            background: rgba(147, 51, 234, 0.5);
            border-radius: 3px;
        }

        .message {
            animation: messageSlideIn 0.3s ease-out;
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .typing-dots {
            display: flex;
            gap: 4px;
            align-items: center;
        }

        .typing-dots span {
            width: 6px;
            height: 6px;
            background: #9333ea;
            border-radius: 50%;
            animation: typingDot 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typingDot {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .chat-input {
            resize: none;
            min-height: 40px;
            max-height: 120px;
        }

        /* انتقالات الصفحات السلسة */
        .page-transition {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        .fade-out {
            animation: fadeOut 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeOut {
            from { opacity: 1; transform: translateY(0); }
            to { opacity: 0; transform: translateY(-10px); }
        }

        .slide-up {
            animation: slideUp 0.4s ease-out;
        }

        @keyframes slideUp {
            from { transform: translateY(100%); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .scale-in {
            animation: scaleIn 0.3s ease-out;
        }

        @keyframes scaleIn {
            from { transform: scale(0.9); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }

        /* Recipe Display Styles */
        .recipe-content h2 {
            color: #c084fc;
            font-size: 1.5rem;
            font-weight: bold;
            margin: 1.5rem 0 1rem 0;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid rgba(192, 132, 252, 0.3);
        }

        .recipe-content h3 {
            color: #a78bfa;
            font-size: 1.2rem;
            font-weight: 600;
            margin: 1.2rem 0 0.8rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .recipe-content ul, .recipe-content ol {
            margin: 0.5rem 0 1rem 1.5rem;
            line-height: 1.8;
        }

        .recipe-content li {
            margin-bottom: 0.5rem;
            color: #e5e7eb;
        }

        .recipe-content ul li {
            list-style: none;
            position: relative;
            padding-right: 1rem;
        }

        .recipe-content ul li::before {
            content: "•";
            color: #fbbf24;
            font-weight: bold;
            position: absolute;
            right: 0;
        }

        .recipe-content ol li {
            background: rgba(139, 92, 246, 0.1);
            padding: 0.5rem;
            border-radius: 0.5rem;
            margin-bottom: 0.8rem;
            border-right: 3px solid #8b5cf6;
        }

        .recipe-content strong {
            color: #fbbf24;
            font-weight: 600;
        }

        .recipe-content p {
            line-height: 1.7;
            margin-bottom: 0.8rem;
            color: #d1d5db;
        }

        /* Project Plan Styles */
        .project-content h2 {
            color: #10b981;
            font-size: 1.5rem;
            font-weight: bold;
            margin: 1.5rem 0 1rem 0;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid rgba(16, 185, 129, 0.3);
        }

        .project-content h3 {
            color: #34d399;
            font-size: 1.2rem;
            font-weight: 600;
            margin: 1.2rem 0 0.8rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(16, 185, 129, 0.1);
            padding: 0.5rem;
            border-radius: 0.5rem;
            border-right: 3px solid #10b981;
        }

        .project-content ul, .project-content ol {
            margin: 0.5rem 0 1rem 1.5rem;
            line-height: 1.8;
        }

        .project-content li {
            margin-bottom: 0.5rem;
            color: #e5e7eb;
            background: rgba(16, 185, 129, 0.05);
            padding: 0.4rem;
            border-radius: 0.3rem;
            border-right: 2px solid rgba(16, 185, 129, 0.3);
        }

        .project-content ul li {
            list-style: none;
            position: relative;
            padding-right: 1rem;
        }

        .project-content ul li::before {
            content: "💰";
            position: absolute;
            right: 0;
        }

        .project-content ol li {
            background: rgba(16, 185, 129, 0.1);
            border-right: 3px solid #10b981;
        }

        .project-content ol li::before {
            content: "📋";
            margin-left: 0.5rem;
        }

        .project-content strong {
            color: #fbbf24;
            font-weight: 600;
        }

        .project-content p {
            line-height: 1.7;
            margin-bottom: 0.8rem;
            color: #d1d5db;
        }

        /* Educational Content Styles */
        .educational-content h2 {
            color: #3b82f6;
            font-size: 1.5rem;
            font-weight: bold;
            margin: 1.5rem 0 1rem 0;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid rgba(59, 130, 246, 0.3);
        }

        .educational-content h3 {
            color: #60a5fa;
            font-size: 1.2rem;
            font-weight: 600;
            margin: 1.2rem 0 0.8rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(59, 130, 246, 0.1);
            padding: 0.5rem;
            border-radius: 0.5rem;
            border-right: 3px solid #3b82f6;
        }

        .educational-content ul, .educational-content ol {
            margin: 0.5rem 0 1rem 1.5rem;
            line-height: 1.8;
        }

        .educational-content li {
            margin-bottom: 0.5rem;
            color: #e5e7eb;
            background: rgba(59, 130, 246, 0.05);
            padding: 0.4rem;
            border-radius: 0.3rem;
            border-right: 2px solid rgba(59, 130, 246, 0.3);
        }

        .educational-content ul li {
            list-style: none;
            position: relative;
            padding-right: 1rem;
        }

        .educational-content ul li::before {
            content: "📚";
            position: absolute;
            right: 0;
        }

        .educational-content ol li {
            background: rgba(59, 130, 246, 0.1);
            border-right: 3px solid #3b82f6;
        }

        .educational-content ol li::before {
            content: "✏️";
            margin-left: 0.5rem;
        }

        .educational-content strong {
            color: #fbbf24;
            font-weight: 600;
        }

        .educational-content p {
            line-height: 1.8;
            margin-bottom: 1rem;
            color: #e5e7eb;
            text-align: justify;
            padding: 0.5rem 0;
        }

        .educational-content .strategy-section {
            background: rgba(59, 130, 246, 0.08);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin: 1rem 0;
            position: relative;
        }

        .educational-content .strategy-section::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(180deg, #3b82f6 0%, #1d4ed8 100%);
            border-radius: 0 0.75rem 0.75rem 0;
        }

        .educational-content .strategy-title {
            color: #60a5fa;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .educational-content .strategy-content {
            color: #d1d5db;
            line-height: 1.7;
        }

        .educational-content .highlight-box {
            background: rgba(251, 191, 36, 0.1);
            border: 1px solid rgba(251, 191, 36, 0.3);
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 0.8rem 0;
            border-right: 3px solid #fbbf24;
        }

        .educational-content .tip-box {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.3);
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 0.8rem 0;
            border-right: 3px solid #22c55e;
        }

        /* Health Content Styles */
        .health-content h2 {
            color: #10b981;
            font-size: 1.5rem;
            font-weight: bold;
            margin: 1.5rem 0 1rem 0;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid rgba(16, 185, 129, 0.3);
        }

        .health-content h3 {
            color: #34d399;
            font-size: 1.2rem;
            font-weight: 600;
            margin: 1.2rem 0 0.8rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(16, 185, 129, 0.1);
            padding: 0.5rem;
            border-radius: 0.5rem;
            border-right: 3px solid #10b981;
        }

        .health-content ul, .health-content ol {
            margin: 0.5rem 0 1rem 1.5rem;
            line-height: 1.8;
        }

        .health-content li {
            margin-bottom: 0.5rem;
            color: #e5e7eb;
            background: rgba(16, 185, 129, 0.05);
            padding: 0.4rem;
            border-radius: 0.3rem;
            border-right: 2px solid rgba(16, 185, 129, 0.3);
        }

        .health-content ul li {
            list-style: none;
            position: relative;
            padding-right: 1rem;
        }

        .health-content ul li::before {
            content: "🏥";
            position: absolute;
            right: 0;
        }

        .health-content ol li {
            background: rgba(16, 185, 129, 0.1);
            border-right: 3px solid #10b981;
        }

        .health-content ol li::before {
            content: "💊";
            margin-left: 0.5rem;
        }

        .health-content strong {
            color: #fbbf24;
            font-weight: 600;
        }

        .health-content p {
            line-height: 1.7;
            margin-bottom: 0.8rem;
            color: #d1d5db;
        }

        .health-content .warning {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            color: #fca5a5;
        }

        /* Story Progressive Styles */
        .story-part {
            animation: fadeInUp 0.5s ease-out;
            position: relative;
        }

        .story-part::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 3px;
            height: 100%;
            background: linear-gradient(180deg, #ec4899 0%, #be185d 100%);
            border-radius: 0 0.5rem 0.5rem 0;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Dealings Content Styles */
        .dealings-content h2 {
            color: #8b5cf6;
            font-size: 1.5rem;
            font-weight: bold;
            margin: 1.5rem 0 1rem 0;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid rgba(139, 92, 246, 0.3);
        }

        .dealings-content h3 {
            color: #a78bfa;
            font-size: 1.2rem;
            font-weight: 600;
            margin: 1.2rem 0 0.8rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(139, 92, 246, 0.1);
            padding: 0.5rem;
            border-radius: 0.5rem;
            border-right: 3px solid #8b5cf6;
        }

        .dealings-content .strategy-section {
            background: rgba(139, 92, 246, 0.08);
            border: 1px solid rgba(139, 92, 246, 0.2);
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin: 1rem 0;
            position: relative;
        }

        .dealings-content .strategy-section::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(180deg, #8b5cf6 0%, #7c3aed 100%);
            border-radius: 0 0.75rem 0.75rem 0;
        }

        .dealings-content .example-box {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.3);
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 0.8rem 0;
            border-right: 3px solid #22c55e;
        }

        .dealings-content .dialogue-box {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 0.8rem 0;
            border-right: 3px solid #3b82f6;
            font-style: italic;
        }

        .dealings-content .tip-box {
            background: rgba(251, 191, 36, 0.1);
            border: 1px solid rgba(251, 191, 36, 0.3);
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 0.8rem 0;
            border-right: 3px solid #fbbf24;
        }
        .custom-loader { width: 50px; height: 50px; border-radius: 50%; background: conic-gradient(var(--main-color), var(--secondary-color)); animation: spin 1s linear infinite; }
        @keyframes spin { 100% { transform: rotate(360deg); } }
        .modal-overlay { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px); z-index: 40; display: flex; align-items: center; justify-content: center; opacity: 0; visibility: hidden; transition: all 0.3s ease; }
        .modal-overlay.active { opacity: 1; visibility: visible; }
        .modal-content { max-height: 80vh; overflow-y: auto; }        .category-btn {
            background: rgba(255, 255, 255, 0.01);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.05);
            position: relative;
            overflow: visible;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
            width: 5rem;
            height: 5rem;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 0.2rem;
        }

        .category-btn svg {
            width: 1.6rem;
            height: 1.6rem;
            flex-shrink: 0;
        }

        .category-btn span {
            font-size: 0.75rem;
            font-weight: 700;
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
            line-height: 1.1;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 4.5rem;
            letter-spacing: 0.02em;
        }
        .category-btn::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border-radius: 50%;
            background: transparent;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            opacity: 0;
            box-shadow: 0 0 0 0 rgba(192, 57, 255, 0);
        }
        .category-btn.active {
            transform: scale(1.05);
            background-color: rgba(192, 57, 255, 0.15);
        }        .category-btn.active::before {
            opacity: 1;
            box-shadow: 0 0 8px 1px rgba(192, 57, 255, 0.4);
            border: 2px solid rgba(192, 57, 255, 0.5);
            animation: neonPulse 3s ease-in-out infinite;
        }

        @keyframes neonPulse {
            0% {
                box-shadow: 0 0 8px 1px rgba(192, 57, 255, 0.4);
                border-color: rgba(192, 57, 255, 0.5);
            }
            50% {
                box-shadow: 0 0 10px 2px rgba(192, 57, 255, 0.5);
                border-color: rgba(192, 57, 255, 0.6);
            }
            100% {
                box-shadow: 0 0 8px 1px rgba(192, 57, 255, 0.4);
                border-color: rgba(192, 57, 255, 0.5);
            }
        }
        .copy-btn { position: absolute; top: 1rem; left: 1rem; background: rgba(255,255,255,0.1); border-radius: 9999px; opacity: 0; transform: scale(0.8); transition: all 0.2s ease-in-out; cursor: pointer; }
        .idea-card:hover .copy-btn { opacity: 1; transform: scale(1); }
        .copy-btn:hover { background: rgba(192, 57, 255, 0.8); }
        #storyOptions.hidden { opacity: 0; transform: translateY(-10px); pointer-events: none; }
        #storyOptions { transition: all 0.3s ease-in-out; }
        .narrator-btn { color: #a0aec0; }
        .narrator-btn.active { color: #ffffff; }
        
        /* Preloader Styles */
        #preloader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: radial-gradient(circle at 60% 40%, #c039ff22 0%, #6a11cb11 40%, #02041a 100%);
            box-shadow: 0 0 40px 10px #c039ff22 inset;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: opacity 0.7s ease-out;
            overflow: hidden;
            opacity: 1;
            pointer-events: auto;
        }
        #preloader.hidden {
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.7s cubic-bezier(0.4,0.2,0.2,1);
        }
        @keyframes preloader-bg-fade {
            0% { filter: blur(6px) brightness(0.8); opacity: 0.3; }
            80% { filter: blur(0px) brightness(1.05); opacity: 1; }
            100% { filter: blur(0px) brightness(1); opacity: 1; }
        }
        #main-app-container { opacity: 0; transition: opacity 0.8s ease-in; }
        #main-app-container.visible { opacity: 1; }
        
        @keyframes word-fade-in {
            from { opacity: 0; transform: translateY(20px) scale(0.9); }
            to { opacity: 1; transform: translateY(0) scale(1); }
        }
        .preloader-title span {
            display: inline-block;
            opacity: 0;
            animation: fade-move-in 0.7s cubic-bezier(0.2,0.8,0.2,1) 0.3s forwards;
            background: linear-gradient(90deg, #c039ff, #6a11cb, #f871b2);
            background-clip: text;
            -webkit-background-clip: text;
            color: transparent;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 2px 4px #c039ff33);
            font-size: 2rem;
            letter-spacing: 0.5px;
        }
        .preloader-title {
            background: none;
            text-align: center;
            font-weight: bold;
            margin-top: 1rem;
            margin-bottom: 0.5rem;
            user-select: none;
        }
        
        @keyframes look-at-text-then-return {
            0%, 100% { transform: translate(0, 0); }
            20%, 80% { transform: translate(0, 8px); }
        }
        
        @keyframes look-around {
            0%, 100% { transform: translate(0, 0); }
            25% { transform: translate(-3px, -2px) scale(0.95); }
            40% { transform: translate(4px, 2px) scale(1.05); }
            60% { transform: translate(2px, -3px); }
            80% { transform: translate(-2px, 3px); }
        }
        @keyframes blink-wink {
            0%, 80%, 100% { transform: scaleY(1); }
            82% { transform: scaleY(0.1); }
            84% { transform: scaleY(1); }
        }
        .preloader-icon .pupil {
            animation-name: look-around, blink-wink;
            animation-duration: 8s, 7s;
            animation-iteration-count: infinite, infinite;
            animation-timing-function: cubic-bezier(0.45, 0, 0.55, 1), ease-in-out;
        }
        .preloader-icon.looking-down .pupil {
            animation-name: look-at-text-then-return !important;
            animation-duration: 3.5s !important;
            animation-timing-function: cubic-bezier(0.6, 0, 0.4, 1) !important;
            animation-iteration-count: 1 !important;
            animation-fill-mode: forwards !important;
            animation-delay: 0s !important;
        }

        /* NEW: Floating animation for main header icon */
        @keyframes float-animation {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }
        .floating-icon {
            animation: float-animation 3s ease-in-out infinite;
            filter: drop-shadow(0 0 20px rgba(192, 57, 255, 0.9)) !important;
        }

        @keyframes fade-move-in {
            from { opacity: 0; transform: translateY(30px) scale(0.9); }
            to { opacity: 1; transform: translateY(0) scale(1); }
        }

        @media (max-width: 600px) {
            html, body {
                font-size: 16px;
                padding: 0;
                margin: 0;
                overflow-x: hidden;
                background: #0d1117;
                width: 100%;
                max-width: 100vw;
            }

            /* تحسين التخطيط العام */
            .container {
                padding: 0.5rem !important;
                margin: 0 !important;
                max-width: 100% !important;
                width: 100% !important;
            }

            /* تحسين شبكة الأزرار */
            .category-selector {
                display: grid !important;
                grid-template-columns: repeat(2, 1fr) !important;
                gap: 0.8rem !important;
                justify-items: center !important;
                align-items: center !important;
                padding: 1rem 0.5rem !important;
                max-width: 100% !important;
                width: 100% !important;
            }

            /* تحسين عرض الأزرار في الشبكة */
            .category-selector .category-btn {
                width: 5rem !important;
                height: 5rem !important;
                margin: 0 !important;
            }

            /* تحسين المؤثرات الخلفية للهاتف */
            body::before {
                content: '' !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                height: 100% !important;
                background:
                    radial-gradient(circle at 20% 30%, rgba(192, 57, 255, 0.6) 0%, transparent 60%),
                    radial-gradient(circle at 80% 70%, rgba(106, 17, 203, 0.7) 0%, transparent 60%),
                    radial-gradient(circle at 50% 50%, rgba(88, 24, 69, 0.5) 0%, transparent 70%),
                    radial-gradient(circle at 40% 60%, rgba(88, 24, 69, 0.6) 0%, transparent 70%),
                    radial-gradient(circle at 60% 40%, rgba(192, 57, 255, 0.5) 0%, transparent 70%) !important;
                z-index: -1 !important;
                filter: blur(0px) !important;
                animation: mobileBackgroundShift 20s ease-in-out infinite !important;
                will-change: background !important;
                transform: translateZ(0) !important;
            }

            #particles-js {
                opacity: 0.8;
            }

            /* تحسينات شاملة لعرض الجوال */
            /* تنسيق الأيقونات والأقسام - محسن لجميع الهواتف */
            .category-btn {
                width: 5rem !important;
                height: 5rem !important;
                padding: 0.4rem !important;
                margin: 0.3rem auto !important;
                border-radius: 50% !important;
                background: rgba(80, 30, 120, 0.12) !important;
                backdrop-filter: blur(8px) !important;
                -webkit-backdrop-filter: blur(8px) !important;
                border: 1px solid rgba(192, 57, 255, 0.2) !important;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
                display: flex !important;
                flex-direction: column !important;
                align-items: center !important;
                justify-content: center !important;
                position: relative !important;
                overflow: visible !important;
                min-width: 5rem !important;
                min-height: 5rem !important;
                gap: 0.2rem !important;
            }
            .category-btn svg {
                width: 1.6rem !important;
                height: 1.6rem !important;
                margin-bottom: 0 !important;
                filter: drop-shadow(0 2px 4px rgba(192, 57, 255, 0.3)) !important;
                opacity: 0.9 !important;
                transition: all 0.3s ease !important;
                flex-shrink: 0 !important;
            }
            
            .category-btn:hover svg {
                opacity: 1 !important;
                transform: scale(1.05) !important;
                filter: drop-shadow(0 3px 6px rgba(192, 57, 255, 0.4)) !important;
            }
            
            .category-btn.active svg {
                opacity: 1 !important;
                transform: scale(1.1) !important;
                filter: drop-shadow(0 3px 8px rgba(192, 57, 255, 0.5)) !important;
            }
            .category-btn span {
                font-size: 0.75rem !important;
                font-weight: 700 !important;
                color: rgba(255, 255, 255, 0.95) !important;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7) !important;
                line-height: 1.1 !important;
                transition: all 0.3s ease !important;
                text-align: center !important;
                white-space: nowrap !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
                max-width: 4.5rem !important;
                display: block !important;
                margin-top: 0.1rem !important;
                letter-spacing: 0.02em !important;
            }
            
            .category-btn:hover span {
                color: rgba(255, 255, 255, 0.95) !important;
                text-shadow: 0 2px 4px rgba(192, 57, 255, 0.3) !important;
            }
            
            .category-btn.active span {
                color: white !important;
                text-shadow: 0 2px 6px rgba(192, 57, 255, 0.4) !important;
                font-weight: 700 !important;
            }

            #categorySelector {
                display: grid !important;
                grid-template-columns: repeat(3, 1fr) !important;
                gap: 0.5rem !important;
                padding: 0.8rem 0.4rem !important;
                justify-items: center !important;
                align-items: center !important;
                width: 100% !important;
                max-width: 400px !important;
                margin: 0 auto !important;
            }

            /* تحسين عرض الاقتراحات */            #suggestionContainer {
                display: grid !important;
                grid-template-columns: 1fr 1fr !important;
                grid-template-rows: auto auto !important;
                gap: 0.5rem !important;
                padding: 0.6rem 0.4rem !important;
                width: 100% !important;
                max-width: 400px !important;
                margin: 0.5rem auto !important;
            }

            #suggestionContainer button {
                font-size: 0.875rem !important;
                padding: 0.5rem 1rem !important;
                border-radius: 1.5rem !important;
                background: rgba(192, 57, 255, 0.1) !important;
                color: rgba(255, 255, 255, 0.9) !important;
                border: 1px solid rgba(192, 57, 255, 0.2) !important;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
                transition: all 0.3s ease !important;
                text-align: center !important;
                backdrop-filter: blur(4px) !important;
                -webkit-backdrop-filter: blur(4px) !important;
                width: 100% !important;
            }

            #suggestionContainer button:nth-child(3) {
                grid-column: 1 / -1 !important;
                width: 70% !important;
                margin: 0 auto !important;
            }

            #suggestionContainer button:hover {
                background: rgba(192, 57, 255, 0.2) !important;
                transform: translateY(-1px) !important;
                box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1) !important;
            }

            #suggestionContainer button:active {
                transform: translateY(1px) !important;
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
            }

            #suggestionContainer button:active {
                transform: scale(0.95) translateY(1px) !important;
                background: rgba(192, 57, 255, 0.25) !important;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
            }

            /* تحسين تأثيرات التفاعل */
            .category-btn:active {
                transform: scale(0.92) translateY(2px) !important;
                background: rgba(192, 57, 255, 0.35) !important;
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15) !important;
            }            .category-btn::before {
                content: '' !important;
                position: absolute !important;
                top: -2px !important;
                left: -2px !important;
                right: -2px !important;
                bottom: -2px !important;
                border-radius: 50% !important;
                background: transparent !important;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
                opacity: 0 !important;
            }
            
            .category-btn.active {
                transform: scale(1.05) !important;
                background: rgba(192, 57, 255, 0.2) !important;
                z-index: 1 !important;
            }            .category-btn.active::before {
                opacity: 1 !important;
                box-shadow: 0 0 8px 1px rgba(192, 57, 255, 0.4) !important;
                border: 2px solid rgba(192, 57, 255, 0.5) !important;
                animation: mobileNeonPulse 3s ease-in-out infinite !important;
            }

            @keyframes mobileNeonPulse {
                0% {
                    box-shadow: 0 0 8px 1px rgba(192, 57, 255, 0.4);
                    border-color: rgba(192, 57, 255, 0.5);
                }
                50% {
                    box-shadow: 0 0 10px 2px rgba(192, 57, 255, 0.5);
                    border-color: rgba(192, 57, 255, 0.6);
                }
                100% {
                    box-shadow: 0 0 8px 1px rgba(192, 57, 255, 0.4);
                    border-color: rgba(192, 57, 255, 0.5);
                }
            }

            .category-btn.active span {
                color: white !important;
                font-weight: 700 !important;
            }            /* تحسين المظهر العام */
            .glassmorphism {
                background: rgba(15, 20, 35, 0.25) !important;
                backdrop-filter: blur(12px) !important;
                -webkit-backdrop-filter: blur(12px) !important;
                border: 1px solid rgba(255, 255, 255, 0.04) !important;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
            }
        }

        /* تحسينات التفاعل على الجوال */
        @media (max-width: 600px) {
            .main-button {
                padding: 1rem 2.5rem !important;
                font-size: 1.1rem !important;
                border-radius: 0.75rem !important;
                width: auto !important;
                min-width: 140px !important;
                margin: 0 auto !important;
            }

            .main-button:active {
                transform: scale(0.98) !important;
            }

            .main-button:hover {
                transform: translateY(-1px) !important;
            }

            .category-btn:active {
                transform: scale(0.95) !important;
                background: rgba(192, 57, 255, 0.2) !important;
            }

            input:focus {
                box-shadow: 0 0 0 3px rgba(192, 57, 255, 0.3) !important;
            }

            /* تحسين المسافات والهوامش */
            main {
                margin: 0 !important;
                padding: 1rem 0.5rem !important;
            }

            .flex-col {
                gap: 1rem !important;
            }

            /* تحسين حجم النصوص */
            button, input, textarea {
                font-size: 16px !important;
            }

            /* تحسين تنسيق النصوص */
            .idea-card h3 {
                font-size: 1.1rem !important;
                line-height: 1.3 !important;
                margin-bottom: 0.75rem !important;
                word-wrap: break-word !important;
                overflow-wrap: break-word !important;
            }

            .idea-card p {
                font-size: 0.9rem !important;
                line-height: 1.5 !important;
                word-wrap: break-word !important;
                overflow-wrap: break-word !important;
                white-space: pre-wrap !important;
            }

            /* تحسين الأزرار */
            .expand-btn {
                font-size: 0.85rem !important;
                padding: 0.5rem 1rem !important;
                word-wrap: break-word !important;
            }
        }


    </style>
</head>
<body class="min-h-screen p-4">
    <div id="particles-js"></div>

    <!-- Preloader -->
    <div id="preloader">
        <div class="text-center">
             <div class="preloader-icon w-32 h-32 mx-auto mb-4" style="filter: drop-shadow(0 0 20px rgba(192, 57, 255, 0.7));">
                 <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                     <defs><linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" stop-color="#c039ff" /><stop offset="100%" stop-color="#6a11cb" /></linearGradient></defs>
                     <path fill="url(#iconGradient)" d="M50,15.2C25.4,15.2,16.5,35.7,18,50.7c1.4,14,13.2,34.1,32,34.1s30.6-20.1,32-34.1 C83.5,35.7,74.6,15.2,50,15.2z"/>
                     <g class="pupil"><circle cx="42" cy="55" r="5" fill="white" /></g>
                     <g class="pupil"><circle cx="58" cy="55" r="5" fill="white" /></g>
                 </svg>
             </div>
             <h1 class="preloader-title text-3xl md:text-4xl font-extrabold mb-4"></h1>
        </div>
    </div>
    
    <div id="main-app-container">
        <div class="fixed top-5 left-5 z-20">
            <button onclick="toggleHistoryLog()" class="p-3 glassmorphism rounded-full hover:bg-purple-500/30 transition-colors relative" title="سجل الإلهام">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M6 1h6v1H6V1zM5.5 2.5A.5.5 0 0 0 5 3v1a.5.5 0 0 0 .5.5h5a.5.5 0 0 0 .5-.5V3a.5.5 0 0 0-.5-.5h-5zM8 8.5a.5.5 0 0 1 .5.5v3a.5.5 0 0 1-1 0v-3a.5.5 0 0 1 .5-.5z"/>
                    <path d="M8 3.5a.5.5 0 0 0-1 0V9a.5.5 0 0 0 .252.434l3.5 2a.5.5 0 0 0 .496-.868L8 8.71V3.5z"/>
                    <path d="M12 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h8zm-1 0H5v1h6V1z"/>
                </svg>
                <div id="historyStatus" class="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full opacity-75" title="السجل يعمل"></div>
            </button>
        </div>

        <div class="relative z-10 container mx-auto max-w-4xl w-full flex flex-col justify-center min-h-screen">
            <header class="text-center mb-6 animate-slide-in" style="animation-delay: 0.1s;">
                 <!-- ADDED floating-icon class for animation -->
                 <div class="w-20 h-20 mx-auto mb-4 floating-icon" style="filter: drop-shadow(0 0 15px rgba(192, 57, 255, 0.7));">
                     <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="iconGradientHeader" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" stop-color="#c039ff" /><stop offset="100%" stop-color="#6a11cb" /></linearGradient></defs><path fill="url(#iconGradientHeader)" d="M50,15.2C25.4,15.2,16.5,35.7,18,50.7c1.4,14,13.2,34.1,32,34.1s30.6-20.1,32-34.1 C83.5,35.7,74.6,15.2,50,15.2z"/><circle cx="42" cy="55" r="5" fill="white" /><circle cx="58" cy="55" r="5" fill="white" /></svg>
                 </div>
                 <h1 class="text-3xl font-extrabold mb-3 text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-pink-400 to-indigo-400">العقل المبدع</h1>
                 <p id="appSubtitle" class="text-gray-300 transition-all duration-300">دردشة ذكية مع خبراء متخصصين</p>
            </header>

            <!-- قائمة الخبراء -->
            <div class="glassmorphism shadow-2xl p-6 md:p-8 mb-8 animate-slide-in" style="animation-delay: 0.3s;">
                <h2 class="text-xl font-bold mb-4 text-center text-purple-300">اختر خبيرك المتخصص</h2>
                <div id="expertSelector" class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6"></div>
            </div>

            <!-- منطقة الدردشة -->
            <div id="chatContainer" class="glassmorphism shadow-2xl p-6 md:p-8 mb-8 animate-slide-in hidden" style="animation-delay: 0.5s;">
                <!-- رأس الدردشة -->
                <div class="flex items-center justify-between mb-6 pb-4 border-b border-gray-600/30">
                    <div class="flex items-center gap-3">
                        <div id="expertAvatar" class="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold text-lg"></div>
                        <div>
                            <h3 id="expertName" class="font-bold text-lg text-white"></h3>
                            <p id="expertRole" class="text-sm text-gray-400"></p>
                        </div>
                    </div>
                    <button onclick="backToExperts()" class="p-2 hover:bg-gray-700/50 rounded-lg transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                            <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"/>
                        </svg>
                    </button>
                </div>

                <!-- منطقة الرسائل -->
                <div id="messagesContainer" class="messages-container mb-4 p-4 bg-gray-900/20 rounded-lg border border-gray-700/30">
                    <!-- الرسائل ستظهر هنا -->
                </div>

                <!-- منطقة الكتابة -->
                <div class="flex gap-3">
                    <textarea id="messageInput" placeholder="اكتب رسالتك هنا..." class="chat-input flex-1 p-3 bg-gray-900/30 backdrop-filter backdrop-blur-md border-2 border-gray-700/40 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-white placeholder-gray-400 resize-none" rows="1"></textarea>
                    <button id="sendButton" class="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                            <path d="m12.14 8.753-5.482 4.796c-.646.566-1.658.106-1.658-.753V3.204a1 1 0 0 1 1.659-.753l5.48 4.796a1 1 0 0 1 0 1.506z"/>
                        </svg>
                    </button>
                </div>
            </div>



            <!-- منطقة الاقتراحات السريعة -->
            <div id="quickSuggestions" class="glassmorphism shadow-2xl p-6 md:p-8 mb-8 animate-slide-in hidden" style="animation-delay: 0.7s;">
                <h3 class="text-lg font-bold mb-4 text-center text-purple-300">اقتراحات سريعة</h3>
                <div id="suggestionButtons" class="grid grid-cols-1 md:grid-cols-2 gap-3"></div>
            </div>
            </div>
        </div>
        
        <!-- نافذة سجل الإلهام -->
        <div id="historyLogModal" class="modal-overlay" onclick="closeHistoryOnOverlay(event)">
            <div class="glassmorphism w-11/12 max-w-2xl p-6 rounded-2xl modal-content">
                <div class="flex justify-between items-center mb-4 border-b border-gray-700 pb-2">
                    <div>
                        <h2 class="text-2xl font-bold">سجل الإلهام</h2>
                        <p id="historyModeIndicator" class="text-xs text-gray-400 mt-1">جاري التحقق من حالة الاتصال...</p>
                    </div>
                    <button onclick="clearHistory()" class="p-2 rounded-full text-red-400 hover:bg-red-500/30 transition-colors" title="حذف السجل بالكامل">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                            <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                        </svg>
                    </button>
                </div>
                <div id="historyLogContent" class="space-y-4">
                    <p>جاري تحميل السجل...</p>
                </div>
            </div>
        </div>


    </div>
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
        import { getAuth, signInAnonymously, onAuthStateChanged, signInWithCustomToken } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js";
        import { getFirestore, collection, addDoc, query, onSnapshot, serverTimestamp, getDocs, deleteDoc, orderBy, limit } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";

        // --- GLOBAL VARIABLES & CONFIG ---
        window.db = null; window.auth = null; window.userId = null;
        const appId = 'creative-mind';
        let currentExpert = null;
        let chatHistory = [];
        let currentSuggestionIndex = -1; // للتنقل بالأسهم

        // بيانات الخبراء المتخصصين
        const experts = {
            story: {
                name: "أستاذ سرد",
                role: "خبير في فنون الكتابة والسرد",
                avatar: "📚",
                color: "from-pink-500 to-rose-500",
                prompt: `أنت أستاذ سرد محترف ومؤلف مشهور، متخصص في فنون الكتابة الإبداعية والسرد العربي الحديث.

تتميز بـ:
- خبرة واسعة في جميع أنواع القصص (رومانسية، مغامرات، خيال علمي، تشويق، دراما)
- قدرة على تطوير الشخصيات وبناء الأحداث
- معرفة عميقة بتقنيات السرد المختلفة
- أسلوب تعليمي ممتع وتفاعلي

عندما يسألك المستخدم:
1. قدم إجابات مفصلة ومنظمة
2. اعطِ أمثلة عملية وقابلة للتطبيق
3. اقترح تمارين أو تطبيقات عملية
4. استخدم أسلوباً ودوداً ومشجعاً
5. نظم إجابتك بعناوين واضحة وترقيم

أجب دائماً باللغة العربية بأسلوب احترافي وودود.`,
                suggestions: [
                    "كيف أكتب بداية مشوقة لقصتي؟",
                    "ما هي عناصر القصة الناجحة؟",
                    "كيف أطور شخصيات مقنعة؟",
                    "أريد كتابة قصة خيال علمي"
                ]
            },
            kitchen: {
                name: "الشيف محمد",
                role: "خبير الطبخ والمأكولات العربية",
                avatar: "👨‍🍳",
                color: "from-orange-500 to-red-500",
                prompt: `أنت الشيف محمد، خبير طبخ محترف ومتخصص في المأكولات العربية والعالمية.

تتميز بـ:
- خبرة 20 سنة في فنون الطبخ
- معرفة واسعة بالمكونات والتوابل
- قدرة على تحويل المكونات البسيطة لأطباق رائعة
- خبرة في الطبخ الصحي والتغذية السليمة

عندما يسألك المستخدم:
1. قدم وصفات مفصلة وواضحة
2. اذكر المكونات بالكميات الدقيقة
3. اشرح خطوات التحضير بالتفصيل
4. قدم نصائح مهمة للنجاح
5. اقترح بدائل للمكونات إذا لم تتوفر
6. نظم الوصفة بشكل جميل ومرتب

أجب دائماً باللغة العربية بأسلوب ودود ومشجع.`,
                suggestions: [
                    "أريد وصفة كبسة دجاج لذيذة",
                    "كيف أحضر معكرونة بالصلصة؟",
                    "وصفات صحية للإفطار",
                    "أطباق سريعة للعشاء"
                ]
            },
            health: {
                name: "د. سارة",
                role: "طبيبة وخبيرة تغذية",
                avatar: "👩‍⚕️",
                color: "from-green-500 to-emerald-500",
                prompt: `أنت الدكتورة سارة، طبيبة متخصصة في الطب العام والتغذية العلاجية.

تتميز بـ:
- خبرة طبية واسعة في الصحة العامة
- تخصص في التغذية والطب الوقائي
- معرفة بالطب البديل والعلاجات الطبيعية
- أسلوب علمي مبسط وواضح

عندما يسألك المستخدم:
1. قدم معلومات طبية دقيقة ومبسطة
2. اذكر 3 طرق للعلاج: طبية، طبيعية، إضافية
3. قدم نصائح وقائية مهمة
4. انصح بمراجعة الطبيب عند الحاجة
5. نظم إجابتك بعناوين واضحة

⚠️ تنبيه: المعلومات للإرشاد فقط وليست بديلاً عن استشارة طبية متخصصة.

أجب دائماً باللغة العربية بأسلوب علمي مبسط.`,
                suggestions: [
                    "كيف أتخلص من الصداع طبيعياً؟",
                    "نصائح لتقوية المناعة",
                    "علاج طبيعي للأرق",
                    "كيف أحافظ على وزن صحي؟"
                ]
            },
            education: {
                name: "أستاذ أحمد",
                role: "خبير تعليمي ومدرس متخصص",
                avatar: "👨‍🏫",
                color: "from-blue-500 to-indigo-500",
                prompt: `أنت الأستاذ أحمد، مدرس محترف وخبير تعليمي متخصص في جميع المواد الدراسية.

تتميز بـ:
- خبرة 15 سنة في التدريس
- قدرة على تبسيط المفاهيم المعقدة
- استخدام طرق تعليمية حديثة وممتعة
- صبر وحكمة في التعامل مع الطلاب

عندما يسألك المستخدم:
1. اشرح المفهوم بطريقة مبسطة ومتدرجة
2. استخدم أمثلة عملية من الحياة
3. قدم تمارين وأنشطة تطبيقية
4. نظم المعلومات بشكل منطقي ومرتب
5. شجع على التفكير والاستكشاف
6. استخدم الرسوم والمخططات عند الحاجة

أجب دائماً باللغة العربية بأسلوب تعليمي ممتع ومشجع.`,
                suggestions: [
                    "اشرح لي قوانين نيوتن بطريقة بسيطة",
                    "كيف أحل مسائل الرياضيات بسهولة؟",
                    "علمني قواعد اللغة العربية",
                    "ما هي أهمية التاريخ؟"
                ]
            },
            project: {
                name: "مستشار الأعمال",
                role: "خبير ريادة الأعمال والاستثمار",
                avatar: "💼",
                color: "from-purple-500 to-violet-500",
                prompt: `أنت مستشار أعمال محترف وخبير في ريادة الأعمال والاستثمار.

تتميز بـ:
- خبرة واسعة في إدارة الأعمال والمشاريع
- معرفة عميقة بالأسواق والاستثمارات
- قدرة على تحليل الفرص التجارية
- خبرة في التخطيط المالي والتسويق

عندما يسألك المستخدم:
1. قدم تحليلاً شاملاً للفكرة أو المشروع
2. اذكر المتطلبات والتكاليف المتوقعة
3. قدم خطة عمل مبسطة
4. اذكر المخاطر والفرص
5. قدم نصائح عملية للبدء
6. نظم إجابتك بعناوين واضحة

أجب دائماً باللغة العربية بأسلوب مهني ومفيد.`,
                suggestions: [
                    "أريد بدء مشروع صغير من المنزل",
                    "كيف أستثمر مبلغ صغير بأمان؟",
                    "فكرة مشروع تجارة إلكترونية",
                    "نصائح لإدارة الأموال الشخصية"
                ]
            },
            dealings: {
                name: "مستشار العلاقات",
                role: "خبير التعامل والعلاقات الإنسانية",
                avatar: "🤝",
                color: "from-teal-500 to-cyan-500",
                prompt: `أنت مستشار متخصص في العلاقات الإنسانية وفنون التعامل مع الآخرين.

تتميز بـ:
- خبرة في علم النفس الاجتماعي
- فهم عميق للسلوك البشري
- مهارات عالية في التواصل
- حكمة في حل المشاكل الاجتماعية

عندما يسألك المستخدم:
1. قدم نصائح عملية وقابلة للتطبيق
2. اشرح الأسباب النفسية وراء السلوكيات
3. قدم استراتيجيات للتعامل مع المواقف
4. استخدم أمثلة واقعية
5. كن متفهماً ومتعاطفاً
6. نظم النصائح بشكل واضح ومرتب

أجب دائماً باللغة العربية بأسلوب حكيم ومتفهم.`,
                suggestions: [
                    "كيف أتعامل مع الأشخاص الصعبين؟",
                    "طرق تحسين مهارات التواصل",
                    "كيف أكسب ثقة الآخرين؟",
                    "حل الخلافات في العمل"
                ]
            }
        };

        // دوال نظام الدردشة
        function renderExperts() {
            const expertSelector = document.getElementById('expertSelector');
            expertSelector.innerHTML = Object.keys(experts).map(key => {
                const expert = experts[key];
                return `
                    <div onclick="selectExpert('${key}')" class="expert-card glassmorphism p-4 rounded-xl cursor-pointer hover:scale-105 transition-all duration-300 text-center">
                        <div class="w-16 h-16 mx-auto mb-3 rounded-full bg-gradient-to-r ${expert.color} flex items-center justify-center text-2xl">
                            ${expert.avatar}
                        </div>
                        <h3 class="font-bold text-white mb-1">${expert.name}</h3>
                        <p class="text-xs text-gray-400">${expert.role}</p>
                    </div>
                `;
            }).join('');
        }

        function selectExpert(expertKey) {
            currentExpert = experts[expertKey];
            chatHistory = [];

            // إخفاء قائمة الخبراء وإظهار الدردشة
            document.querySelector('.glassmorphism').style.display = 'none';
            document.getElementById('chatContainer').classList.remove('hidden');
            document.getElementById('quickSuggestions').classList.remove('hidden');

            // تحديث معلومات الخبير
            document.getElementById('expertAvatar').textContent = currentExpert.avatar;
            document.getElementById('expertAvatar').className = `w-12 h-12 rounded-full bg-gradient-to-r ${currentExpert.color} flex items-center justify-center text-white font-bold text-lg`;
            document.getElementById('expertName').textContent = currentExpert.name;
            document.getElementById('expertRole').textContent = currentExpert.role;

            // إضافة رسالة ترحيب
            addMessage('expert', `مرحباً! أنا ${currentExpert.name}، ${currentExpert.role}. كيف يمكنني مساعدتك اليوم؟`);

            // عرض الاقتراحات السريعة
            renderQuickSuggestions();
        }

        function renderQuickSuggestions() {
            const suggestionButtons = document.getElementById('suggestionButtons');
            suggestionButtons.innerHTML = currentExpert.suggestions.map(suggestion => `
                <button onclick="sendQuickMessage('${suggestion}')" class="p-3 text-sm bg-gray-800/50 hover:bg-purple-600/30 rounded-lg transition-colors text-right">
                    ${suggestion}
                </button>
            `).join('');
        }

        function backToExperts() {
            document.querySelector('.glassmorphism').style.display = 'block';
            document.getElementById('chatContainer').classList.add('hidden');
            document.getElementById('quickSuggestions').classList.add('hidden');
            currentExpert = null;
            chatHistory = [];
        }
        function addMessage(sender, content) {
            const messagesContainer = document.getElementById('messagesContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message mb-4 animate-slide-in`;

            if (sender === 'user') {
                messageDiv.innerHTML = `
                    <div class="flex justify-end">
                        <div class="bg-purple-600 text-white p-3 rounded-lg max-w-xs lg:max-w-md text-right">
                            ${content}
                        </div>
                    </div>
                `;
            } else {
                messageDiv.innerHTML = `
                    <div class="flex items-start gap-3">
                        <div class="w-8 h-8 rounded-full bg-gradient-to-r ${currentExpert.color} flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                            ${currentExpert.avatar}
                        </div>
                        <div class="bg-gray-800/50 text-white p-3 rounded-lg max-w-xs lg:max-w-md text-right">
                            ${content}
                        </div>
                    </div>
                `;
            }

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            // إضافة للتاريخ
            chatHistory.push({ sender, content, timestamp: new Date().toISOString() });
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message || !currentExpert) return;

            // إضافة رسالة المستخدم
            addMessage('user', message);
            input.value = '';

            // إظهار مؤشر الكتابة
            showTypingIndicator();

            // إرسال للذكاء الاصطناعي
            setTimeout(() => {
                generateExpertResponse(message);
            }, 1000);
        }

        function sendQuickMessage(message) {
            if (!currentExpert) return;

            // إضافة رسالة المستخدم
            addMessage('user', message);

            // إظهار مؤشر الكتابة
            showTypingIndicator();

            // إرسال للذكاء الاصطناعي
            setTimeout(() => {
                generateExpertResponse(message);
            }, 1000);
        }

        function showTypingIndicator() {
            const messagesContainer = document.getElementById('messagesContainer');
            const typingDiv = document.createElement('div');
            typingDiv.id = 'typingIndicator';
            typingDiv.className = 'message expert-message mb-4';
            typingDiv.innerHTML = `
                <div class="flex items-start gap-3">
                    <div class="w-8 h-8 rounded-full bg-gradient-to-r ${currentExpert.color} flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                        ${currentExpert.avatar}
                    </div>
                    <div class="bg-gray-800/50 text-white p-3 rounded-lg">
                        <div class="typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>
            `;

            messagesContainer.appendChild(typingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        async function generateExpertResponse(userMessage) {
            try {
                const prompt = `${currentExpert.prompt}

سؤال المستخدم: ${userMessage}

تاريخ المحادثة السابق:
${chatHistory.slice(-4).map(msg => `${msg.sender === 'user' ? 'المستخدم' : currentExpert.name}: ${msg.content}`).join('\n')}

يرجى الإجابة بشكل مفصل ومفيد ومنظم.`;

                const response = await callGeminiAPI(prompt);

                hideTypingIndicator();
                addMessage('expert', response);

                // حفظ في التاريخ
                saveToHistory(userMessage, response, currentExpert.name);

            } catch (error) {
                console.error('خطأ في توليد الرد:', error);
                hideTypingIndicator();
                addMessage('expert', 'عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.');
            }
        }

        // حفظ محادثة في التاريخ
        function saveToHistory(userMessage, expertResponse, expertName) {
            const historyItem = {
                id: Date.now().toString(),
                timestamp: new Date().toISOString(),
                type: 'chat',
                expert: expertName,
                conversation: [
                    { sender: 'user', message: userMessage },
                    { sender: 'expert', message: expertResponse }
                ]
            };

            try {
                // حفظ محلي
                let localHistory = JSON.parse(localStorage.getItem('chatHistory') || '[]');
                localHistory.unshift(historyItem);

                // الاحتفاظ بآخر 50 محادثة فقط
                if (localHistory.length > 50) {
                    localHistory = localHistory.slice(0, 50);
                }

                localStorage.setItem('chatHistory', JSON.stringify(localHistory));

                // حفظ في Firebase إذا كان متاح
                if (window.db && window.userId) {
                    window.db.collection('users').doc(window.userId).collection('chatHistory').add(historyItem)
                        .then(() => console.log('تم حفظ المحادثة في Firebase'))
                        .catch(error => console.log('خطأ في حفظ المحادثة:', error));
                }
            } catch (error) {
                console.error('خطأ في حفظ التاريخ:', error);
            }
        }


        const categoryConfig = {
            story: {
                name: "قصة", key: 'story',
                icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16"><path d="M1 2.828c.885-.37 2.154-.769 3.388-.985 1.234-.217 2.522.256 3.447.854.925.598 1.943.933 2.98.583 1.037-.35 2.122-.733 3.142-.828.985-.094 1.99.165 2.829.583v9.932c-.885.37-2.154.769-3.388.985-1.234-.217-2.522-.256-3.447-.854-.925-.598-1.943-.933-2.98-.583-1.037.35-2.122-.733-3.142.828-.985.094-1.99-.165-2.829-.583V2.828z"/><path d="M3 5.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zM3 8a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9A.5.5 0 0 1 3 8zm0 2.5a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5z"/></svg>`,
                placeholder: "اكتب فكرة قصة (مثال: مستكشف يجد مدينة مفقودة)...",
                suggestions: ["رائد فضاء وحيد", "جريمة في قطار فاخر", "مغامرة في عالم سحري", "قصة حب في زمن الحرب", "لغز في مكتبة قديمة"],
                mainPrompt: `أنت روائي محترف متمكن من فنون السرد العربي الحديث. اقترح 4 بدايات قصص مختلفة حول موضوع: [USER_INPUT]

لكل قصة قدم:
- عنوان جذاب ومشوق للقصة
- نوع القصة (مغامرة، رومانسية، خيال علمي، تشويق، دراما، كوميديا)
- بداية مشوقة للقصة (150-200 كلمة) تتضمن:
  * تقديم الشخصية الرئيسية
  * وصف المكان والزمان
  * حدث مثير يجذب القارئ
  * نهاية مفتوحة تدفع للمتابعة

معايير الكتابة:
- ابدأ بمشهد مثير أو حوار جذاب
- اجعل القارئ يتساءل "ماذا سيحدث بعد ذلك؟"
- استخدم تقنيات التشويق والإثارة
- لا تكشف الكثير من الأحداث
- اترك القارئ متشوق للمتابعة

يجب أن يكون الرد باللغة العربية حصراً.`,
                expandPrompt: `أنت روائي محترف، أكمل القصة '[TITLE]' بالجزء التالي.

القصة حتى الآن: [EXISTING_TEXT]

اكتب الجزء التالي (200-300 كلمة) مع:
1. تطوير الأحداث بشكل طبيعي ومشوق
2. تعميق الشخصيات وإظهار مشاعرها
3. إضافة تفاصيل مثيرة وغير متوقعة
4. استخدام حوارات طبيعية ومعبرة
5. وصف الأماكن والأجواء بجمال
6. إنهاء الجزء بتشويق يدفع للمتابعة

ملاحظة مهمة:
- إذا كانت هذه المرة الثالثة أو أكثر لإكمال القصة، اكتب نهاية مؤثرة ومُرضية للقصة
- لا تكرر الأحداث السابقة
- حافظ على تسلسل منطقي للأحداث

اكتب باللغة العربية الفصحى مع تقسيم لفقرات منظمة.`,
                expandButtonText: "أكمل القصة"
            },
            kitchen: {
                name: "مطبخ", key: 'kitchen',
                icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16"><path d="M6.5 1A1.5 1.5 0 0 0 5 2.5V3H1.5A1.5 1.5 0 0 0 0 4.5v8A1.5 1.5 0 0 0 1.5 14h13a1.5 1.5 0 0 0 1.5-1.5v-8A1.5 1.5 0 0 0 14.5 3H11v-.5A1.5 1.5 0 0 0 9.5 1h-3zm0 1h3a.5.5 0 0 1 .5.5V3H6v-.5a.5.5 0 0 1 .5-.5zM1.5 4h13a.5.5 0 0 1 .5.5V13a.5.5 0 0 1-.5.5h-13a.5.5 0 0 1-.5-.5V4.5a.5.5 0 0 1 .5-.5z"/></svg>`,
                placeholder: "اكتب المكونات المتوفرة لديك (مثال: دجاج، أرز)...",
                suggestions: ["لحم، بطاطس، بصل", "بيض، جبن، خبز", "معكرونة، صلصة طماطم", "أرز، خضار، دجاج", "سمك، ليمون، أعشاب"],
                mainPrompt: `أنت شيف عالمي خبير في فنون الطبخ والابتكار الغذائي. مهمتك ابتكار 4 وصفات متنوعة باستخدام المكونات: [USER_INPUT]

معايير الابتكار:
- امزج بين المطابخ العالمية والعربية
- اقترح تقنيات طبخ مختلفة (شوي، قلي، طبخ بطيء، إلخ)
- راعي التوازن الغذائي والطعم
- أضف لمسات إبداعية في التقديم
- اقترح بدائل للمكونات غير المتوفرة

يجب أن يكون الرد باللغة العربية حصراً.`,
                expandPrompt: `قدم وصفة '[TITLE]' بالتنسيق التالي بالضبط:

## 🍽️ [TITLE]

### 📋 المكونات:
• [قائمة المكونات مع الكميات الدقيقة]

### 👨‍🍳 طريقة التحضير:
1. [خطوة أولى مفصلة]
2. [خطوة ثانية مفصلة]
3. [باقي الخطوات...]

### ⏰ معلومات الطبق:
• وقت التحضير: [الوقت]
• وقت الطبخ: [الوقت]
• عدد الأشخاص: [العدد]
• مستوى الصعوبة: [سهل/متوسط/صعب]

### 💡 نصائح الشيف:
• [نصائح مهمة لنجاح الطبق]

### 🎨 التقديم والتزيين:
• [طرق التقديم والتزيين]

### 🔄 بدائل المكونات:
• [بدائل للمكونات غير المتوفرة]

استخدم هذا التنسيق بالضبط مع الرموز والعناوين كما هي.`,
                expandButtonText: "عرض الوصفة بالتفصيل"
            },
            health: {
                name: "صحة", key: 'health',
                icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16"><path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/><path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/></svg>`,
                placeholder: "اكتب مشكلة صحية أو موضوع تريد معرفة طرق علاجه (مثال: الصداع، الأرق، آلام الظهر)...",
                suggestions: ["الصداع", "الأرق", "آلام الظهر", "نزلات البرد", "القلق", "الإجهاد", "آلام المعدة", "الحساسية"],
                mainPrompt: `أنت طبيب استشاري خبير، اقترح 3 طرق لعلاج أو التعامل مع: [USER_INPUT]

الطرق المطلوبة بالضبط:
1. الطريقة الطبية: العلاج الطبي المعتمد والأدوية المناسبة
2. الطريقة المنزلية: العلاجات الطبيعية والوصفات المنزلية الآمنة
3. الطريقة الإضافية: نصائح وقائية أو علاجات بديلة مكملة

تأكد من:
- تقديم معلومات طبية دقيقة وآمنة
- التنبيه لضرورة استشارة الطبيب عند الحاجة
- ذكر أي تحذيرات أو احتياطات مهمة
- استخدام لغة واضحة ومفهومة

ملاحظة: هذه المعلومات للتثقيف الصحي ولا تغني عن استشارة الطبيب المختص.`,
                expandButtonText: "عرض التفاصيل الطبية",
                expandPrompt: `قدم شرحاً طبي مفصلاً لعلاج '[TITLE]' بالتنسيق التالي:

## 🏥 العلاج الطبي المفصل: [TITLE]

### 💊 العلاج الدوائي:
• [الأدوية المناسبة والجرعات]

### 🔬 التشخيص والفحوصات:
• [الفحوصات المطلوبة والتشخيص]

### 🏠 العلاج المنزلي:
• [الطرق الطبيعية والآمنة]

### ⚠️ التحذيرات والاحتياطات:
• [تحذيرات مهمة ومتى تستشير الطبيب]

### 🛡️ الوقاية:
• [طرق الوقاية من تكرار المشكلة]

### 📋 خطة العلاج:
1. [خطوة أولى]
2. [خطوة ثانية]
3. [باقي الخطوات...]

### 🔄 المتابعة:
• [كيفية متابعة التحسن والشفاء]

تذكر: هذه المعلومات للتثقيف الصحي فقط ولا تغني عن استشارة الطبيب المختص.`,
            },
            educational: {
                name: "تعليم", key: 'educational',
                icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16"><path d="M8.211 2.047a.5.5 0 0 0-.422 0l-7.5 3.5a.5.5 0 0 0 .025.917l7.5 3a.5.5 0 0 0 .372 0L14 7.14V13a1 1 0 0 0-1 1v2h3v-2a1 1 0 0 0-1-1V6.739l.686-.275a.5.5 0 0 0 .025-.917l-7.5-3.5zM8 8.46 1.758 5.965 8 3.052l6.242 2.913L8 8.46z"/><path d="M4.176 9.032a.5.5 0 0 0-.656.327l-.5 1.7a.5.5 0 0 0 .294.605l4.5 1.8a.5.5 0 0 0 .372 0l4.5-1.8a.5.5 0 0 0 .294-.605l-.5-1.7a.5.5 0 0 0-.656-.327L8 10.466 4.176 9.032z"/></svg>`,
                placeholder: "اكتب أي موضوع أو مادة تريد تعلمها (مثال: الرياضيات، الفيزياء، التاريخ، البرمجة)...",
                suggestions: ["الرياضيات", "الفيزياء", "الكيمياء", "التاريخ", "الجغرافيا", "البرمجة", "اللغة الإنجليزية", "الأحياء", "الاقتصاد", "الفلسفة"],
                mainPrompt: `أنت أستاذ خصوصي خبير ومتخصص في: [USER_INPUT]. مهمتك تقديم 4 طرق تعليمية مختلفة ومبتكرة لشرح هذا الموضوع.

كأستاذ خصوصي محترف:
- افهم مستوى الطالب واحتياجاته
- استخدم أساليب تعليمية متنوعة (بصرية، سمعية، عملية)
- اجعل التعلم ممتع وتفاعلي
- استخدم أمثلة من الواقع والحياة اليومية
- قدم طرق مختلفة تناسب أنماط التعلم المختلفة
- اربط المعلومات الجديدة بما يعرفه الطالب مسبق

قدم كل طريقة بعنوان واضح ووصف مفصل لكيفية التطبيق.`,
                expandPrompt: `أنت أستاذ خصوصي متخصص، اشرح '[TITLE]' بالتنسيق التالي:

## 🎓 شرح: [TITLE]

### 📚 المفهوم الأساسي:
• [شرح بسيط ومفهوم للموضوع]

### 🔍 التفاصيل المهمة:
• [النقاط الرئيسية والتفاصيل الضرورية]

### 💡 أمثلة من الحياة:
• [أمثلة واقعية يمكن للطالب فهمها بسهولة]

### 📝 خطوات التطبيق:
1. [خطوة أولى واضحة]
2. [خطوة ثانية مفصلة]
3. [باقي الخطوات...]

### 🎯 تمارين عملية:
• [تمارين وأنشطة للتطبيق]

### 🧠 نصائح للحفظ والفهم:
• [طرق تساعد على التذكر والفهم العميق]

### ❓ أسئلة للمراجعة:
• [أسئلة لاختبار الفهم]

### 📖 للتوسع أكثر:
• [مواضيع مرتبطة ومصادر إضافية]

استخدم لغة بسيطة وواضحة مناسبة لجميع المستويات.`,
                expandButtonText: "عرض الشرح التفصيلي"
            },
            project: {
                name: "مشاريع", key: 'project',
                icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16"><path d="M8.5 5.5a.5.5 0 0 0-1 0v3.793L6.354 8.146a.5.5 0 1 0-.708.708l2 2a.5.5 0 0 0 .708 0l2-2a.5.5 0 0 0-.708-.708L8.5 9.293V5.5z"/><path d="M3 0h10a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2v-1h1v1a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H3a1 1 0 0 0-1 1v1H1V2a2 2 0 0 1 2-2z"/><path d="M1 5v-.5a.5.5 0 0 1 1 0V5h.5a.5.5 0 0 1 0 1h-2a.5.5 0 0 1 0-1H1zm0 3v-.5a.5.5 0 0 1 1 0V8h.5a.5.5 0 0 1 0 1h-2a.5.5 0 0 1 0-1H1zm0 3v-.5a.5.5 0 0 1 1 0v.5h.5a.5.5 0 0 1 0 1h-2a.5.5 0 0 1 0-1H1z"/></svg>`,
                placeholder: "اكتب فكرة أو مجال تريد الاستثمار فيه (مثال: مقهى، تطبيق، خدمة توصيل)...",
                suggestions: ["مقهى متخصص", "تطبيق جوال", "خدمة توصيل", "متجر إلكتروني", "مركز تدريب", "مشروع غذائي", "خدمة تنظيف", "ورشة حرفية"],
                mainPrompt: `أنت مستشار استثماري خبير في المشاريع الصغيرة والمتوسطة. اقترح 4 أفكار مشاريع مربحة مبنية على: [USER_INPUT]

ركز على:
- مشاريع بسيطة وقابلة للتنفيذ
- رأس مال معقول (من 5,000 إلى 100,000 ريال)
- عائد استثماري واضح
- سوق محلي أو إقليمي
- إمكانية البدء من المنزل أو مساحة صغيرة

اجعل كل فكرة عملية ومباشرة مع تقدير أولي للتكلفة والربح المتوقع.`,
                expandPrompt: `ضع خطة استثمارية مبسطة لمشروع '[TITLE]' بالتنسيق التالي:

## 💼 خطة مشروع: [TITLE]

### 💰 التكاليف والاستثمار:
• رأس المال المطلوب: [المبلغ]
• التكاليف الشهرية: [المبلغ]
• نقطة التعادل: [المدة]

### 📊 الأرباح المتوقعة:
• الإيرادات الشهرية المتوقعة: [المبلغ]
• هامش الربح: [النسبة]
• العائد على الاستثمار: [النسبة سنو]

### 🎯 الجمهور المستهدف:
• [وصف الفئة المستهدفة]

### 📈 استراتيجية التسويق:
• [طرق التسويق البسيطة والفعالة]

### ⚠️ المخاطر الرئيسية:
• [أهم 3 مخاطر وكيفية تجنبها]

### 🚀 خطوات البدء:
1. [الخطوة الأولى]
2. [الخطوة الثانية]
3. [الخطوة الثالثة]

استخدم أرقام واقعية وعملية للسوق السعودي/الخليجي.`,
                expandButtonText: "عرض خطة العمل"
            },
            dealings: {
                name: "تعاملات", key: 'dealings',
                icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16"><path d="M14 1a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H4.414A2 2 0 0 0 3 11.586l-2 2V2a1 1 0 0 1 1-1h12zM2 0a2 2 0 0 0-2 2v12.793a.5.5 0 0 0 .854.353l2.853-2.853A1 1 0 0 1 4.414 12H14a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2z"/><path d="M5 6a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm4 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm4 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"/></svg>`,
                placeholder: "اكتب موقفًا اجتماعي (مثال: كيفية بدء حوار)...",
                suggestions: ["الرد على نقد غير بناء", "طلب زيادة في الراتب", "الاعتذار بشكل فعال", "التعامل مع الصراعات", "بناء علاقات مهنية"],
                mainPrompt: `أنت خبير علم النفس الاجتماعي والتواصل الإنساني. قدم 5 استراتيجيات ذكية للتعامل مع: [USER_INPUT]

أسس التواصل الفعال:
- اطبق مبادئ علم النفس السلوكي
- استخدم تقنيات التواصل اللاعنفي
- راعي الذكاء العاطفي والاجتماعي
- اقترح حلولاً عملية وقابلة للتطبيق
- احترم الثقافة العربية والقيم الاجتماعية

يجب أن يكون الرد باللغة العربية حصراً.`,
                expandPrompt: `اشرح استراتيجية '[TITLE]' بالتنسيق التالي:

## 🧠 شرح الاستراتيجية: [TITLE]

### 🔍 السبب النفسي والاجتماعي:
• [لماذا تعمل هذه الاستراتيجية من الناحية النفسية]

### 📋 خطوات التطبيق:
1. [الخطوة الأولى بالتفصيل]
2. [الخطوة الثانية بالتفصيل]
3. [الخطوة الثالثة بالتفصيل]

### 💬 مثال على الحوار:
**الموقف:** [وصف الموقف]
**أنت:** "[ما تقوله بالضبط]"
**الطرف الآخر:** "[رد محتمل]"
**أنت:** "[ردك التالي]"

### 🎯 العبارات المقترحة:
• "[عبارة محددة 1]"
• "[عبارة محددة 2]"
• "[عبارة محددة 3]"

### ⏰ التوقيت المناسب:
• [متى تستخدم هذه الاستراتيجية]

### ✅ النتائج المتوقعة:
• [الفوائد والنتائج الإيجابية]

### ❌ أخطاء يجب تجنبها:
• [الأخطاء الشائعة وكيفية تجنبها]

### 💡 نصائح للنجاح:
• [نصائح عملية لتطبيق ناجح]

استخدم أمثلة واقعية وحوارات كاملة باللغة العربية.`,
                expandButtonText: "شرح الاستراتيجية"
            },

            health: {
                name: "صحة", key: 'health',
                icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16"><path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/><path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/></svg>`,
                placeholder: "اكتب سؤال صح أو نمط حياة تريد تحسينه...",
                suggestions: ["تمارين للعمل من المنزل", "نظام غذائي لزيادة الطاقة", "طرق تقليل التوتر", "تحسين جودة النوم", "تقوية المناعة"],
                mainPrompt: `أنت طبيب ومدرب صحة معتمد متخصص في الطب الوقائي ونمط الحياة الصحي. قدم 5 نصائح أو حلول صحية للموضوع: [USER_INPUT]

معايير النصائح الصحية:
- اعتمد على الأدلة العلمية والدراسات الطبية
- قدم حلولاً عملية وقابلة للتطبيق في الحياة اليومية
- راعي الفروق الفردية والحالات الصحية المختلفة
- اجعل النصائح آمنة ومناسبة للعامة
- أضف تحذيرات عند الحاجة لاستشارة طبية

يجب أن يكون الرد باللغة العربية حصراً مع التأكيد على أن هذه نصائح عامة وليست بديلاً عن الاستشارة الطبية.`,
                expandPrompt: `بالتفصيل وباللغة العربية، اشرح النصيحة الصحية '[TITLE]'.

قدم:
1. الأساس العلمي وراء هذه النصيحة
2. خطة تطبيق عملية مع جدول زمني
3. الفوائد المتوقعة على المدى القصير والطويل
4. احتياطات ومحاذير مهمة
5. طرق قياس التقدم والنتائج
6. بدائل للأشخاص ذوي الظروف الخاصة
7. نصائح للاستمرارية والدافعية

تذكير: هذه نصائح عامة وليست بديلاً عن الاستشارة الطبية المتخصصة.`,
                expandButtonText: "عرض الخطة الصحية"
            },


        };

        // --- DOM Elements ---
        const userInput = document.getElementById('userInput');
        const resultsContainer = document.getElementById('results');
        const loader = document.getElementById('loader');
        const placeholder = document.getElementById('placeholder');
        const categorySelector = document.getElementById('categorySelector');
        const suggestionContainer = document.getElementById('suggestionContainer');
        const storyOptionsContainer = document.getElementById('storyOptions');
        const historyLogModal = document.getElementById('historyLogModal');
        const historyLogContent = document.getElementById('historyLogContent');

        // --- PWA SERVICE WORKER ---
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('./sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful with scope: ', registration.scope);
                    }, function(err) {
                        console.log('ServiceWorker registration failed: ', err);
                    });
            });
        }

        // --- INITIALIZATION ---
        document.addEventListener('DOMContentLoaded', () => {
            const preloader = document.getElementById('preloader');
            const mainApp = document.getElementById('main-app-container');
            
            // إصلاح مشكلة عنوان شاشة التحميل
            const titleContainer = document.querySelector('.preloader-title');
            if (titleContainer) {
                const titleText = "العقل المبدع";
                titleContainer.innerHTML = titleText;
                titleContainer.style.opacity = '1';
            }
            
            // تحسين سرعة تحميل الأيقونة
            const preloaderIcon = document.querySelector('.preloader-icon');
            if (preloaderIcon) {
                preloaderIcon.classList.add('looking-down');
            }
            
            // إخفاء شاشة التحميل بعد فترة محددة
            setTimeout(() => {
                if (preloader) preloader.classList.add('hidden');
                if (mainApp) mainApp.classList.add('visible');
                setTimeout(() => {
                    if (preloader) preloader.style.display = 'none';
                }, 300);
            }, 2000); // تقليل وقت الانتظار من 4000 إلى 2000 مللي ثانية
            
            setupFirebase();
            renderExperts();
            renderCategoryButtons();
            setupChatEventListeners();
            setupMainEventListeners();
            updateHistoryStatus(false);
            cleanupOldHistory();

            // اختيار الفئة الافتراضية
            selectCategory('story');
            console.log("تم تحميل التطبيق مع الميزات الجديدة");
        });

        // إعداد مستمعات الأحداث للدردشة
        function setupChatEventListeners() {
            // مستمع إرسال الرسالة بالضغط على Enter
            const messageInput = document.getElementById('messageInput');
            if (messageInput) {
                messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendMessage();
                    }
                });
            }

            // مستمع زر الإرسال
            const sendButton = document.getElementById('sendButton');
            if (sendButton) {
                sendButton.addEventListener('click', sendMessage);
            }
        }

        // الدوال القديمة (محفوظة للتوافق)
        function oldEventListeners() {
            const userInput = document.getElementById('userInput');
            if (!userInput) return;

            // مراقب تغيير النص لإعادة تعيين حالة الضغط والاقتراحات
            userInput.addEventListener('input', (e) => {
                const currentText = userInput.value.trim();
                if (currentText !== lastTopic) {
                    isGenerating = false;
                    const generateBtn = document.getElementById('generateBtn');
                    generateBtn.disabled = false;
                    generateBtn.textContent = 'ألهمني';
                    generateBtn.style.opacity = '1';
                }

                // عرض الاقتراحات التلقائية
                showAutoSuggestions(currentText);
            });

            // مراقب الأسهم للتنقل في الاقتراحات
            userInput.addEventListener('keydown', (e) => {
                const suggestionsContainer = document.getElementById('autoSuggestions');
                const suggestions = suggestionsContainer.querySelectorAll('.suggestion-item');

                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    currentSuggestionIndex = Math.min(currentSuggestionIndex + 1, suggestions.length - 1);
                    updateSuggestionSelection(suggestions);
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    currentSuggestionIndex = Math.max(currentSuggestionIndex - 1, -1);
                    updateSuggestionSelection(suggestions);
                } else if (e.key === 'Enter' && currentSuggestionIndex >= 0) {
                    e.preventDefault();
                    suggestions[currentSuggestionIndex].click();
                } else if (e.key === 'Escape') {
                    hideSuggestions();
                }
            });

            // إخفاء الاقتراحات عند النقر خارجها
            document.addEventListener('click', (e) => {
                if (!e.target.closest('#userInput') && !e.target.closest('#autoSuggestions')) {
                    hideSuggestions();
                }
            });
        }

        // --- RENDER & SELECTION FUNCTIONS ---
        function renderCategoryButtons() {
            categorySelector.innerHTML = Object.keys(categoryConfig).map(key => `
                <button id="cat-btn-${key}" onclick="selectCategory('${key}')" class="category-btn p-3 rounded-full flex flex-col items-center gap-2 w-16 h-16 sm:w-20 sm:h-20 justify-center" title="${categoryConfig[key].name}">
                    ${categoryConfig[key].icon}
                    <span class="text-xs mt-1">${categoryConfig[key].name}</span>
                </button>
            `).join('');
        }
        
        window.selectCategory = (key) => {
            const newCategory = categoryConfig[key];
            if (!newCategory) return;

            // انتقال سلس للمحتوى
            const mainContent = document.querySelector('.main-content');
            if (mainContent) mainContent.classList.add('fade-out');

            setTimeout(() => {
                currentCategory = newCategory;
                const activeBtn = document.getElementById(`cat-btn-${key}`);

                document.querySelectorAll('.category-btn').forEach(btn => btn.classList.remove('active'));
                activeBtn.classList.add('active');

                storyOptionsContainer.classList.toggle('hidden', key !== 'story');

                userInput.placeholder = currentCategory.placeholder;
                userInput.value = ''; // Clear input on category change

                displayDynamicSuggestions();

                // Clear results with animation
                if (resultsContainer.children.length > 0) {
                    resultsContainer.classList.add('fade-out');
                    setTimeout(() => {
                        resultsContainer.innerHTML = '';
                        if (placeholder) {
                            placeholder.style.display = 'block';
                            resultsContainer.appendChild(placeholder);
                        }
                        resultsContainer.classList.remove('fade-out');
                        resultsContainer.classList.add('fade-in');
                    }, 200);
                } else {
                    resultsContainer.innerHTML = '';
                    if (placeholder) {
                        placeholder.style.display = 'block';
                        resultsContainer.appendChild(placeholder);
                    }
                }

                if (mainContent) {
                    mainContent.classList.remove('fade-out');
                    mainContent.classList.add('fade-in');
                }

                // إخفاء الاقتراحات عند تغيير القسم
                hideSuggestions();

                setTimeout(() => {
                    if (mainContent) mainContent.classList.remove('fade-in');
                    resultsContainer.classList.remove('fade-in');
                }, 500);
            }, 150);
        };

        function displayDynamicSuggestions() {
            suggestionContainer.innerHTML = '';
            if (!currentCategory || !currentCategory.suggestions) return;
            
            const suggestions = [...currentCategory.suggestions]; 
            for (let i = suggestions.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [suggestions[i], suggestions[j]] = [suggestions[j], suggestions[i]];
            }

            const suggestionsToShow = suggestions.slice(0, 3); 
            
            suggestionsToShow.forEach((s, i) => {
                const btn = document.createElement('button');
                btn.className = "bg-gray-700/50 px-3 py-1.5 text-xs rounded-full hover:bg-purple-500/50 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-600 animate-slide-in";
                btn.style.animationDelay = `${i * 0.05}s`;
                btn.innerText = s;
                btn.onclick = () => setExample(s);
                suggestionContainer.appendChild(btn);
            });
        }

        window.selectNarratorStyle = (style) => {
            currentNarratorStyle = style;
            const highlight = document.getElementById('narrator-highlight');
            const protagonistBtn = document.getElementById('narrator-protagonist');
            const narratorBtn = document.getElementById('narrator-narrator');
            
            if (style === 'protagonist') {
                highlight.style.transform = 'translateX(0%)';
                protagonistBtn.classList.add('active');
                narratorBtn.classList.remove('active');
            } else {
                highlight.style.transform = 'translateX(-100%)';
                narratorBtn.classList.add('active');
                protagonistBtn.classList.remove('active');
            }
        };

        window.setExample = (text) => {
            userInput.value = text;
            userInput.focus();
            // إعادة تعيين حالة الضغط عند تغيير النص
            lastTopic = '';
            isGenerating = false;
            hideSuggestions();
        };

        // وظائف الاقتراحات التلقائية
        function showAutoSuggestions(query) {
            const suggestionsContainer = document.getElementById('autoSuggestions');

            if (!currentCategory || query.length < 2) {
                hideSuggestions();
                return;
            }

            const categoryKey = currentCategory.key;
            const suggestions = autoSuggestionsDB[categoryKey] || [];

            // فلترة الاقتراحات بناءً على النص المدخل
            const filteredSuggestions = suggestions.filter(suggestion =>
                suggestion.toLowerCase().includes(query.toLowerCase()) ||
                query.toLowerCase().includes(suggestion.toLowerCase())
            );

            if (filteredSuggestions.length === 0) {
                hideSuggestions();
                return;
            }

            // عرض الاقتراحات
            suggestionsContainer.innerHTML = filteredSuggestions.slice(0, 6).map(suggestion => `
                <div class="suggestion-item" onclick="selectSuggestion('${suggestion.replace(/'/g, "\\'")}')">
                    <div class="suggestion-category">${currentCategory.name}</div>
                    <div class="suggestion-text">${suggestion}</div>
                </div>
            `).join('');

            suggestionsContainer.classList.remove('hidden');
            currentSuggestionIndex = -1;
        }

        function hideSuggestions() {
            const suggestionsContainer = document.getElementById('autoSuggestions');
            suggestionsContainer.classList.add('hidden');
            currentSuggestionIndex = -1;
        }

        function updateSuggestionSelection(suggestions) {
            suggestions.forEach((item, index) => {
                if (index === currentSuggestionIndex) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
        }

        window.selectSuggestion = (text) => {
            userInput.value = text;
            hideSuggestions();
            userInput.focus();
            lastTopic = '';
            isGenerating = false;
        };





        // دالة تنظيف السجل القديم
        function cleanupOldHistory() {
            try {
                const historyKey = 'creative_mind_history';
                let history = JSON.parse(localStorage.getItem(historyKey) || '[]');

                if (history.length === 0) return;

                const sevenDaysAgo = new Date();
                sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

                const originalLength = history.length;

                // فلترة السجلات - احتفظ بالحديثة فقط (آخر 7 أيام)
                const cleanedHistory = history.filter(item => {
                    const itemDate = new Date(item.createdAt);
                    return itemDate > sevenDaysAgo;
                });

                // حفظ السجل المنظف إذا تغير
                if (cleanedHistory.length !== originalLength) {
                    localStorage.setItem(historyKey, JSON.stringify(cleanedHistory));
                    console.log(`تم حذف ${originalLength - cleanedHistory.length} عنصر قديم من السجل`);
                }
            } catch (error) {
                console.error("Error cleaning up old history:", error);
            }
        }

        // --- RENDER & SELECTION FUNCTIONS ---
        function renderCategoryButtons() {
            if (!categorySelector) return;
            categorySelector.innerHTML = Object.keys(categoryConfig).map(key => `
                <button id="cat-btn-${key}" onclick="selectCategory('${key}')" class="category-btn p-3 rounded-full flex flex-col items-center gap-2 w-16 h-16 sm:w-20 sm:h-20 justify-center" title="${categoryConfig[key].name}">
                    ${categoryConfig[key].icon}
                    <span class="text-xs mt-1">${categoryConfig[key].name}</span>
                </button>
            `).join('');
        }

        window.selectCategory = (key) => {
            const newCategory = categoryConfig[key];
            if (!newCategory) return;

            // انتقال سلس للمحتوى
            const mainContent = document.querySelector('.main-content');
            if (mainContent) mainContent.classList.add('fade-out');

            setTimeout(() => {
                currentCategory = newCategory;
                const activeBtn = document.getElementById(`cat-btn-${key}`);

                // إزالة التحديد من جميع الأزرار
                document.querySelectorAll('.category-btn').forEach(btn => {
                    btn.classList.remove('active', 'bg-purple-600', 'text-white', 'shadow-lg', 'scale-110');
                    btn.classList.add('bg-gray-700/50', 'text-gray-300', 'hover:bg-purple-500/50');
                });

                // تحديد الزر النشط
                if (activeBtn) {
                    activeBtn.classList.add('active', 'bg-purple-600', 'text-white', 'shadow-lg', 'scale-110');
                    activeBtn.classList.remove('bg-gray-700/50', 'text-gray-300', 'hover:bg-purple-500/50');
                }

                // إظهار/إخفاء خيارات السرد
                if (storyOptionsContainer) {
                    if (key === 'story') {
                        storyOptionsContainer.classList.remove('hidden');
                    } else {
                        storyOptionsContainer.classList.add('hidden');
                    }
                }

                // تحديث placeholder
                if (userInput) {
                    userInput.placeholder = `اكتب ${currentCategory.name} هنا...`;
                }

                // تحديث الاقتراحات
                updateSuggestions();

                // إزالة تأثير الانتقال
                if (mainContent) mainContent.classList.remove('fade-out');
            }, 150);
        };

        function updateSuggestions() {
            if (!suggestionContainer || !currentCategory) return;

            suggestionContainer.innerHTML = '';

            const suggestions = [...currentCategory.suggestions];
            for (let i = suggestions.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [suggestions[i], suggestions[j]] = [suggestions[j], suggestions[i]];
            }

            const suggestionsToShow = suggestions.slice(0, 3);

            suggestionsToShow.forEach((s, i) => {
                const btn = document.createElement('button');
                btn.className = "bg-gray-700/50 px-3 py-1.5 text-xs rounded-full hover:bg-purple-500/50 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-600 animate-slide-in";
                btn.style.animationDelay = `${i * 0.1}s`;
                btn.textContent = s;
                btn.onclick = () => { if (userInput) userInput.value = s; };
                suggestionContainer.appendChild(btn);
            });
        }

        window.selectNarratorStyle = (style) => {
            currentNarratorStyle = style;
            const highlight = document.getElementById('narrator-highlight');
            const protagonistBtn = document.getElementById('narrator-protagonist');
            const narratorBtn = document.getElementById('narrator-narrator');

            // إزالة التحديد من جميع الأزرار
            [protagonistBtn, narratorBtn].forEach(btn => {
                if (btn) {
                    btn.classList.remove('bg-pink-600', 'text-white');
                    btn.classList.add('bg-gray-700/50', 'text-gray-300');
                }
            });

            // تحديد الزر النشط
            const activeBtn = style === 'protagonist' ? protagonistBtn : narratorBtn;
            if (activeBtn) {
                activeBtn.classList.add('bg-pink-600', 'text-white');
                activeBtn.classList.remove('bg-gray-700/50', 'text-gray-300');
            }

            // تحريك المؤشر
            if (highlight) {
                highlight.style.transform = style === 'protagonist' ? 'translateX(0)' : 'translateX(100%)';
                highlight.classList.remove('scale-x-0');
                highlight.classList.add('scale-x-100');
            }
        };

        // إعداد مستمعات الأحداث للواجهة الرئيسية
        function setupMainEventListeners() {
            if (!userInput) return;

            // مستمع الكتابة للاقتراحات التلقائية
            userInput.addEventListener('input', (e) => {
                const query = e.target.value.trim();
                if (query.length > 1) {
                    showAutoSuggestions(query);
                } else {
                    hideSuggestions();
                }
            });

            // مستمع الضغط على Enter
            userInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    generateIdeas();
                }

                const suggestionsContainer = document.getElementById('autoSuggestions');
                const suggestions = suggestionsContainer?.querySelectorAll('.suggestion-item');

                if (suggestions && suggestions.length > 0) {
                    if (e.key === 'ArrowDown') {
                        e.preventDefault();
                        currentSuggestionIndex = Math.min(currentSuggestionIndex + 1, suggestions.length - 1);
                        updateSuggestionHighlight(suggestions);
                    } else if (e.key === 'ArrowUp') {
                        e.preventDefault();
                        currentSuggestionIndex = Math.max(currentSuggestionIndex - 1, -1);
                        updateSuggestionHighlight(suggestions);
                    } else if (e.key === 'Enter' && currentSuggestionIndex >= 0) {
                        e.preventDefault();
                        suggestions[currentSuggestionIndex].click();
                    } else if (e.key === 'Escape') {
                        hideSuggestions();
                    }
                }
            });

            // إخفاء الاقتراحات عند النقر خارجها
            document.addEventListener('click', (e) => {
                if (!e.target.closest('#userInput') && !e.target.closest('#autoSuggestions')) {
                    hideSuggestions();
                }
            });
        }

        function showAutoSuggestions(query) {
            const suggestionsContainer = document.getElementById('autoSuggestions');
            if (!suggestionsContainer || !currentCategory) return;

            currentSuggestionIndex = -1;

            const categoryKey = currentCategory.key;
            const suggestions = autoSuggestionsDB[categoryKey] || [];

            // فلترة الاقتراحات بناءً على النص المدخل
            const filteredSuggestions = suggestions.filter(suggestion =>
                suggestion.toLowerCase().includes(query.toLowerCase()) ||
                query.toLowerCase().includes(suggestion.toLowerCase())
            ).slice(0, 5);

            if (filteredSuggestions.length > 0) {
                suggestionsContainer.innerHTML = filteredSuggestions.map(suggestion => `
                    <div class="suggestion-item p-2 hover:bg-purple-500/30 cursor-pointer transition-colors" onclick="selectSuggestion('${suggestion}')">
                        ${suggestion}
                    </div>
                `).join('');
                suggestionsContainer.classList.remove('hidden');
            } else {
                hideSuggestions();
            }
        }

        function hideSuggestions() {
            const suggestionsContainer = document.getElementById('autoSuggestions');
            if (suggestionsContainer) {
                suggestionsContainer.classList.add('hidden');
                currentSuggestionIndex = -1;
            }
        }

        function updateSuggestionHighlight(suggestions) {
            suggestions.forEach((suggestion, index) => {
                if (index === currentSuggestionIndex) {
                    suggestion.classList.add('bg-purple-500/50');
                } else {
                    suggestion.classList.remove('bg-purple-500/50');
                }
            });
        }

        window.selectSuggestion = (suggestion) => {
            if (userInput) {
                userInput.value = suggestion;
                userInput.focus();
            }
            hideSuggestions();
        };

        // --- CORE LOGIC ---
        let isGenerating = false; // متغير لمنع الضغط المتكرر
        let lastTopic = ''; // متغير لحفظ آخر موضوع
        let currentCategory = null; // الفئة المختارة حالياً
        let currentNarratorStyle = 'protagonist'; // أسلوب السرد الافتراضي

        // قاعدة بيانات الاقتراحات التلقائية
        const autoSuggestionsDB = {
            story: ["مغامرة في الفضاء", "قصة حب رومانسية", "لغز جريمة قتل", "رحلة عبر الزمن", "حرب بين الممالك"],
            kitchen: ["دجاج مشوي", "معكرونة بالصلصة", "سلطة خضراء", "حلويات شرقية", "شوربة عدس"],
            health: ["تمارين رياضية", "نظام غذائي صحي", "علاج الصداع", "تقوية المناعة", "تحسين النوم"],
            educational: ["تعلم البرمجة", "قواعد اللغة العربية", "الرياضيات الأساسية", "التاريخ الإسلامي", "العلوم الطبيعية"],
            project: ["مقهى صغير", "متجر إلكتروني", "تطبيق جوال", "مركز تدريب", "خدمة توصيل"],
            dealings: ["التفاوض الناجح", "حل النزاعات", "بناء الثقة", "التواصل الفعال", "إدارة الوقت"]
        };

        window.generateIdeas = async () => {
            if (!currentCategory) { alert("الرجاء اختيار قسم أولاً."); return; }
            const topic = userInput.value.trim();
            if (!topic) { alert(`الرجاء إدخال ${currentCategory.name} أولاً.`); return; }

            // منع الضغط المتكرر إلا إذا تغير الموضوع
            if (isGenerating && topic === lastTopic) {
                return;
            }

            isGenerating = true;
            lastTopic = topic;

            // تعطيل الزر وتغيير النص
            const generateBtn = document.getElementById('generateBtn');
            generateBtn.disabled = true;
            generateBtn.textContent = 'جاري التفكير...';
            generateBtn.style.opacity = '0.7';

            loader.classList.remove('hidden');
            resultsContainer.innerHTML = '';
            resultsContainer.appendChild(loader);
            if (placeholder) placeholder.style.display = 'none';

            let prompt = (currentCategory.mainPrompt || '').replace('[USER_INPUT]', topic);
            if (currentCategory.key === 'story') {
                const styleText = currentNarratorStyle === 'protagonist' ? 'بطل القصة (منظور الشخص الأول)' : 'الراوي (منظور الشخص الثالث)';
                prompt = prompt.replace('[NARRATIVE_STYLE]', styleText);
            }
            
            try {
                const ideas = await callGemini(prompt, {
                    responseMimeType: "application/json",
                    responseSchema: { type: "OBJECT", properties: { "ideas": { type: "ARRAY", items: { type: "OBJECT", properties: { "title": { type: "STRING" }, "description": { type: "STRING" } }, required: ["title", "description"] } } }, required: ["ideas"] }
                });
                displayIdeas(ideas.ideas, topic);
                saveToHistory(topic, currentCategory.name, ideas.ideas);

                // التمرير التلقائي للنتائج
                setTimeout(() => {
                    const firstIdeaCard = resultsContainer.querySelector('.idea-card');
                    if (firstIdeaCard) {
                        // حساب المسافة المناسبة للتمرير
                        const offset = window.innerHeight * 0.1; // 10% من ارتفاع الشاشة
                        const elementPosition = firstIdeaCard.getBoundingClientRect().top + window.pageYOffset - offset;

                        window.scrollTo({
                            top: elementPosition,
                            behavior: 'smooth'
                        });
                    }
                }, 500);

            } catch (error) {
                 resultsContainer.innerHTML = `<p class="text-center text-red-400 p-4 glassmorphism rounded-lg">حدث خطأ: ${error.message}</p>`;
            } finally {
                loader.classList.add('hidden');

                // إعادة تفعيل الزر
                const generateBtn = document.getElementById('generateBtn');
                generateBtn.disabled = false;
                generateBtn.textContent = 'ألهمني';
                generateBtn.style.opacity = '1';
                isGenerating = false;
            }
        };
        
        window.expandIdea = async (button, originalTopic) => {
            const card = button.closest('.idea-card');
            const cardBody = card.querySelector('.card-body');
            const cardTitle = card.querySelector('h3').innerText;
            const existingText = cardBody.innerText;
            const buttonContainer = button.parentElement;
            
            const loaderId = `loader-${Date.now()}`;
            const tempLoader = document.createElement('div');
            tempLoader.innerHTML = `<div id="${loaderId}" class="flex justify-center items-center p-4"><div class="custom-loader w-8 h-8"></div></div>`;
            
            let prompt;

            if (currentCategory.key === 'story') {
                let clickCount = parseInt(button.dataset.clickCount || '0', 10);
                if (clickCount >= 5) {
                    button.disabled = true;
                    button.innerText = 'انتهت القصة';
                    button.classList.add('opacity-50', 'cursor-not-allowed');
                    return;
                }
                button.dataset.clickCount = clickCount + 1;

                cardBody.appendChild(tempLoader);
                prompt = (currentCategory.expandPrompt || '').replace('[TITLE]', cardTitle).replace('[EXISTING_TEXT]', existingText);

                // تحديد نوع الجزء حسب عدد النقرات
                if (clickCount === 0) {
                    // الجزء الأول - تطوير البداية
                    prompt += "\n\nهذا هو الجزء الأول من القصة. طور الأحداث وأضف تفاصيل مشوقة.";
                } else if (clickCount === 1) {
                    // الجزء الثاني - تصاعد الأحداث
                    prompt += "\n\nهذا هو الجزء الثاني. اجعل الأحداث أكثر إثارة وتعقيد.";
                } else if (clickCount === 2) {
                    // الجزء الثالث - ذروة الأحداث
                    prompt += "\n\nهذا هو الجزء الثالث. اصل للذروة والتشويق الأكبر.";
                } else if (clickCount === 3) {
                    // الجزء الرابع - بداية الحل
                    prompt += "\n\nهذا هو الجزء الرابع. ابدأ في حل العقدة وتوضيح الأمور.";
                } else if (clickCount === 4) {
                    // الجزء الأخير - النهاية
                    prompt += "\n\nهذا هو الجزء الأخير. اكتب نهاية مؤثرة ومُرضية للقصة.";
                    button.innerText = 'اكتب النهاية';
                }
            } else {
                buttonContainer.innerHTML = `<div id="${loaderId}" class="flex justify-center items-center p-4"><div class="custom-loader w-8 h-8"></div></div>`;
                if (currentCategory.key === 'creative') {
                    const lowerTopic = originalTopic.toLowerCase();
                    if (lowerTopic.includes('قصة') || lowerTopic.includes('اكتب')) {
                        prompt = `بصفتك كاتب مبدع وباللغة العربية، اكتب بداية قصة قصيرة (حوالي 150 كلمة) مبنية على الفكرة التالية: '${cardTitle}: ${existingText}'. يجب أن تكون البداية مشوقة وتؤسس لأجواء غامضة أو مثيرة.`;
                    } else if (lowerTopic.includes('اسم') || lowerTopic.includes('أسماء')) {
                        prompt = `بصفتك خبير في العلامات التجارية وباللغة العربية، قدم 5 اقتراحات إضافية ومبتكرة للأسماء بناءً على الفكرة التالية: '${cardTitle}: ${existingText}'. مع شرح بسيط لمعنى كل اسم مقترح ولماذا هو مناسب.`;
                    } else {
                        prompt = (currentCategory.expandPrompt || '').replace('[TITLE]', cardTitle);
                    }
                } else {
                    prompt = (currentCategory.expandPrompt || '').replace('[TITLE]', cardTitle).replace('[DESCRIPTION]', existingText);
                }
            }
            
            try {
                const expansion = await callGemini(prompt, { responseMimeType: "text/plain" });
                let html = expansion;
                
                const loaderEl = document.getElementById(loaderId);
                
                if (currentCategory.key === 'story') {
                    if (loaderEl) loaderEl.parentElement.remove();

                    // إضافة الجزء الجديد مع تنسيق جميل
                    const storyPart = document.createElement('div');
                    storyPart.className = 'story-part mt-4 p-3 bg-gray-800/30 rounded-lg border-r-2 border-pink-500/50';
                    storyPart.innerHTML = `<div class="text-sm text-pink-300 mb-2">الجزء ${parseInt(button.dataset.clickCount, 10)}</div>${html.replace(/\n/g, '<br/>')}`;
                    cardBody.appendChild(storyPart);

                    // تحديث نص الزر
                    const clickCount = parseInt(button.dataset.clickCount, 10);
                    if(clickCount >= 5) {
                        button.disabled = true;
                        button.innerText = 'انتهت القصة';
                        button.classList.add('opacity-50', 'cursor-not-allowed');
                    } else if (clickCount === 4) {
                        button.innerText = 'اكتب النهاية';
                    } else {
                        button.innerText = `أكمل القصة (${clickCount}/5)`;
                    }
                } else {
                    // تحسين عرض حسب نوع القسم
                    if (currentCategory.key === 'kitchen') {
                        // تحويل النص إلى HTML منسق للوصفات
                        html = formatRecipeHTML(html);
                        if(buttonContainer && buttonContainer.parentElement){
                            buttonContainer.parentElement.querySelector('.card-body').innerHTML = `<div class="recipe-content text-sm leading-relaxed">${html}</div>`;
                            buttonContainer.remove();
                        }
                    } else if (currentCategory.key === 'project') {
                        // تحويل النص إلى HTML منسق للمشاريع
                        html = formatProjectHTML(html);
                        if(buttonContainer && buttonContainer.parentElement){
                            buttonContainer.parentElement.querySelector('.card-body').innerHTML = `<div class="project-content text-sm leading-relaxed">${html}</div>`;
                            buttonContainer.remove();
                        }
                    } else if (currentCategory.key === 'educational') {
                        // تحويل النص إلى HTML منسق للتعليم
                        html = formatEducationalHTML(html);
                        if(buttonContainer && buttonContainer.parentElement){
                            buttonContainer.parentElement.querySelector('.card-body').innerHTML = `<div class="educational-content text-sm leading-relaxed">${html}</div>`;
                            buttonContainer.remove();
                        }
                    } else if (currentCategory.key === 'health') {
                        // تحويل النص إلى HTML منسق للصحة
                        html = formatHealthHTML(html);
                        if(buttonContainer && buttonContainer.parentElement){
                            buttonContainer.parentElement.querySelector('.card-body').innerHTML = `<div class="health-content text-sm leading-relaxed">${html}</div>`;
                            buttonContainer.remove();
                        }
                    } else if (currentCategory.key === 'dealings') {
                        // تحويل النص إلى HTML منسق للتعاملات
                        html = formatDealingsHTML(html);
                        if(buttonContainer && buttonContainer.parentElement){
                            buttonContainer.parentElement.querySelector('.card-body').innerHTML = `<div class="dealings-content text-sm leading-relaxed">${html}</div>`;
                            buttonContainer.remove();
                        }
                    } else {
                        // التنسيق العادي للأقسام الأخرى
                        html = html.replace(/\n\n/g, '<br/><br/>').replace(/\n/g, '<br/>').replace(/(\d+\.)/g, '<br/><strong>$1</strong>');
                        if(buttonContainer && buttonContainer.parentElement){
                            buttonContainer.parentElement.querySelector('.card-body').innerHTML = `<div class="text-sm leading-relaxed">${html}</div>`;
                            buttonContainer.remove();
                        }
                    }
                }
            } catch (error) {
                const targetElement = currentCategory.key === 'story' ? cardBody : buttonContainer;
                if (targetElement) {
                    targetElement.innerHTML = `<p class="text-red-400">فشل تطوير الفكرة. ${error.message}</p>`;
                } else {
                     console.error("Target element for error message not found.");
                }
            }
        };

        // دالة تنسيق الوصفات
        function formatRecipeHTML(text) {
            let html = text;

            // تحويل العناوين الرئيسية (##)
            html = html.replace(/## (.*)/g, '<h2>$1</h2>');

            // تحويل العناوين الفرعية (###)
            html = html.replace(/### (.*)/g, '<h3>$1</h3>');

            // تحويل القوائم النقطية (•)
            html = html.replace(/• (.*)/g, '<li>$1</li>');

            // تحويل القوائم المرقمة
            html = html.replace(/(\d+)\. (.*)/g, '<li>$2</li>');

            // تجميع القوائم النقطية
            html = html.replace(/(<li>.*<\/li>)/gs, function(match) {
                if (match.includes('1.') || match.includes('2.') || match.includes('3.')) {
                    return '<ol>' + match + '</ol>';
                } else {
                    return '<ul>' + match + '</ul>';
                }
            });

            // تحويل الأسطر الجديدة
            html = html.replace(/\n\n/g, '</p><p>');
            html = html.replace(/\n/g, '<br/>');

            // إضافة تاغ p في البداية والنهاية
            if (!html.startsWith('<h2>') && !html.startsWith('<h3>')) {
                html = '<p>' + html + '</p>';
            }

            // تنظيف HTML
            html = html.replace(/<p><\/p>/g, '');
            html = html.replace(/<p><h/g, '<h');
            html = html.replace(/<\/h([23])><\/p>/g, '</h$1>');
            html = html.replace(/<p><ul>/g, '<ul>');
            html = html.replace(/<\/ul><\/p>/g, '</ul>');
            html = html.replace(/<p><ol>/g, '<ol>');
            html = html.replace(/<\/ol><\/p>/g, '</ol>');

            return html;
        }

        // دالة تنسيق خطط المشاريع
        function formatProjectHTML(text) {
            let html = text;

            // تحويل العناوين الرئيسية (##)
            html = html.replace(/## (.*)/g, '<h2>$1</h2>');

            // تحويل العناوين الفرعية (###)
            html = html.replace(/### (.*)/g, '<h3>$1</h3>');

            // تحويل القوائم النقطية (•)
            html = html.replace(/• (.*)/g, '<li>$1</li>');

            // تحويل القوائم المرقمة
            html = html.replace(/(\d+)\. (.*)/g, '<li>$2</li>');

            // تجميع القوائم
            html = html.replace(/(<li>.*<\/li>)/gs, function(match) {
                if (match.includes('الخطوة') || match.includes('خطوة') || match.includes('1.') || match.includes('2.')) {
                    return '<ol>' + match + '</ol>';
                } else {
                    return '<ul>' + match + '</ul>';
                }
            });

            // تحويل الأسطر الجديدة
            html = html.replace(/\n\n/g, '</p><p>');
            html = html.replace(/\n/g, '<br/>');

            // إضافة تاغ p في البداية والنهاية
            if (!html.startsWith('<h2>') && !html.startsWith('<h3>')) {
                html = '<p>' + html + '</p>';
            }

            // تنظيف HTML
            html = html.replace(/<p><\/p>/g, '');
            html = html.replace(/<p><h/g, '<h');
            html = html.replace(/<\/h([23])><\/p>/g, '</h$1>');
            html = html.replace(/<p><ul>/g, '<ul>');
            html = html.replace(/<\/ul><\/p>/g, '</ul>');
            html = html.replace(/<p><ol>/g, '<ol>');
            html = html.replace(/<\/ol><\/p>/g, '</ol>');

            // تمييز الأرقام المالية
            html = html.replace(/(\d+,?\d*)\s*(ريال|دولار|درهم)/g, '<strong style="color: #10b981;">$1 $2</strong>');
            html = html.replace(/(\d+)%/g, '<strong style="color: #fbbf24;">$1%</strong>');

            return html;
        }

        // دالة تنسيق المحتوى التعليمي
        function formatEducationalHTML(text) {
            let html = text;

            // تحويل العناوين الرئيسية (##)
            html = html.replace(/## (.*)/g, '<h2>$1</h2>');

            // تحويل العناوين الفرعية (###) مع تنسيق خاص
            html = html.replace(/### (.*)/g, '<div class="strategy-section"><div class="strategy-title">$1</div><div class="strategy-content">');

            // إغلاق أقسام الاستراتيجية
            html = html.replace(/(<div class="strategy-section">[\s\S]*?<div class="strategy-content">)([\s\S]*?)(?=<div class="strategy-section">|<h2>|$)/g, '$1$2</div></div>');

            // تحويل القوائم النقطية (•)
            html = html.replace(/• (.*)/g, '<li>$1</li>');

            // تحويل القوائم المرقمة
            html = html.replace(/(\d+)\. (.*)/g, '<li>$2</li>');

            // تجميع القوائم
            html = html.replace(/(<li>.*<\/li>)/gs, function(match) {
                if (match.includes('خطوة') || match.includes('1.') || match.includes('2.')) {
                    return '<ol>' + match + '</ol>';
                } else {
                    return '<ul>' + match + '</ul>';
                }
            });

            // تحويل النصائح المهمة
            html = html.replace(/(نصيحة|تنبيه|مهم|ملاحظة):\s*(.*?)(?=\n|$)/gi, '<div class="tip-box"><strong>💡 $1:</strong> $2</div>');

            // تحويل النقاط المميزة
            html = html.replace(/(الهدف|الفائدة|المطلوب):\s*(.*?)(?=\n|$)/gi, '<div class="highlight-box"><strong>🎯 $1:</strong> $2</div>');

            // تحويل الأسطر الجديدة
            html = html.replace(/\n\n/g, '</p><p>');
            html = html.replace(/\n/g, '<br/>');

            // إضافة تاغ p في البداية والنهاية
            if (!html.startsWith('<h2>') && !html.startsWith('<div class="strategy-section">')) {
                html = '<p>' + html + '</p>';
            }

            // تنظيف HTML
            html = html.replace(/<p><\/p>/g, '');
            html = html.replace(/<p><h/g, '<h');
            html = html.replace(/<\/h([23])><\/p>/g, '</h$1>');
            html = html.replace(/<p><div/g, '<div');
            html = html.replace(/<\/div><\/p>/g, '</div>');
            html = html.replace(/<p><ul>/g, '<ul>');
            html = html.replace(/<\/ul><\/p>/g, '</ul>');
            html = html.replace(/<p><ol>/g, '<ol>');
            html = html.replace(/<\/ol><\/p>/g, '</ol>');

            // تمييز المصطلحات المهمة
            html = html.replace(/\*\*(.*?)\*\*/g, '<strong style="color: #60a5fa;">$1</strong>');
            html = html.replace(/\*(.*?)\*/g, '<em style="color: #93c5fd;">$1</em>');

            return html;
        }

        // دالة تنسيق المحتوى الصحي
        function formatHealthHTML(text) {
            let html = text;

            // تحويل العناوين الرئيسية (##)
            html = html.replace(/## (.*)/g, '<h2>$1</h2>');

            // تحويل العناوين الفرعية (###)
            html = html.replace(/### (.*)/g, '<h3>$1</h3>');

            // تحويل القوائم النقطية (•)
            html = html.replace(/• (.*)/g, '<li>$1</li>');

            // تحويل القوائم المرقمة
            html = html.replace(/(\d+)\. (.*)/g, '<li>$2</li>');

            // تجميع القوائم
            html = html.replace(/(<li>.*<\/li>)/gs, function(match) {
                if (match.includes('خطوة') || match.includes('1.') || match.includes('2.')) {
                    return '<ol>' + match + '</ol>';
                } else {
                    return '<ul>' + match + '</ul>';
                }
            });

            // تحويل الأسطر الجديدة
            html = html.replace(/\n\n/g, '</p><p>');
            html = html.replace(/\n/g, '<br/>');

            // إضافة تاغ p في البداية والنهاية
            if (!html.startsWith('<h2>') && !html.startsWith('<h3>')) {
                html = '<p>' + html + '</p>';
            }

            // تنظيف HTML
            html = html.replace(/<p><\/p>/g, '');
            html = html.replace(/<p><h/g, '<h');
            html = html.replace(/<\/h([23])><\/p>/g, '</h$1>');
            html = html.replace(/<p><ul>/g, '<ul>');
            html = html.replace(/<\/ul><\/p>/g, '</ul>');
            html = html.replace(/<p><ol>/g, '<ol>');
            html = html.replace(/<\/ol><\/p>/g, '</ol>');

            // تمييز التحذيرات الطبية
            html = html.replace(/(تحذير|احذر|تنبيه|خطر|استشر الطبيب)/gi, '<strong style="color: #ef4444;">$1</strong>');
            html = html.replace(/(ملاحظة.*?:.*?)(<br\/>|<\/p>)/gi, '<div class="warning">⚠️ $1</div>$2');

            // تمييز الأدوية والجرعات
            html = html.replace(/(\d+)\s*(مجم|ملجم|جرام|مل)/g, '<strong style="color: #10b981;">$1 $2</strong>');

            return html;
        }

        // دالة تنسيق محتوى التعاملات
        function formatDealingsHTML(text) {
            let html = text;

            // تحويل العناوين الرئيسية (##)
            html = html.replace(/## (.*)/g, '<h2>$1</h2>');

            // تحويل العناوين الفرعية (###) مع تنسيق خاص
            html = html.replace(/### (.*)/g, '<div class="strategy-section"><h3>$1</h3><div class="strategy-content">');

            // إغلاق أقسام الاستراتيجية
            html = html.replace(/(<div class="strategy-section">[\s\S]*?<div class="strategy-content">)([\s\S]*?)(?=<div class="strategy-section">|<h2>|$)/g, '$1$2</div></div>');

            // تحويل القوائم النقطية (•)
            html = html.replace(/• (.*)/g, '<li>$1</li>');

            // تحويل القوائم المرقمة
            html = html.replace(/(\d+)\. (.*)/g, '<li>$2</li>');

            // تجميع القوائم
            html = html.replace(/(<li>.*<\/li>)/gs, function(match) {
                if (match.includes('خطوة') || match.includes('1.') || match.includes('2.')) {
                    return '<ol>' + match + '</ol>';
                } else {
                    return '<ul>' + match + '</ul>';
                }
            });

            // تحويل الحوارات والأمثلة
            html = html.replace(/\*\*(.*?):\*\*\s*"(.*?)"/g, '<div class="dialogue-box"><strong>$1:</strong> "$2"</div>');

            // تحويل النصائح والأمثلة
            html = html.replace(/(مثال|نصيحة|تنبيه|مهم):\s*(.*?)(?=\n|$)/gi, '<div class="tip-box"><strong>💡 $1:</strong> $2</div>');

            // تحويل الأمثلة العملية
            html = html.replace(/(الموقف|المثال|السيناريو):\s*(.*?)(?=\n|$)/gi, '<div class="example-box"><strong>🎯 $1:</strong> $2</div>');

            // تحويل الأسطر الجديدة
            html = html.replace(/\n\n/g, '</p><p>');
            html = html.replace(/\n/g, '<br/>');

            // إضافة تاغ p في البداية والنهاية
            if (!html.startsWith('<h2>') && !html.startsWith('<div class="strategy-section">')) {
                html = '<p>' + html + '</p>';
            }

            // تنظيف HTML
            html = html.replace(/<p><\/p>/g, '');
            html = html.replace(/<p><h/g, '<h');
            html = html.replace(/<\/h([23])><\/p>/g, '</h$1>');
            html = html.replace(/<p><div/g, '<div');
            html = html.replace(/<\/div><\/p>/g, '</div>');
            html = html.replace(/<p><ul>/g, '<ul>');
            html = html.replace(/<\/ul><\/p>/g, '</ul>');
            html = html.replace(/<p><ol>/g, '<ol>');
            html = html.replace(/<\/ol><\/p>/g, '</ol>');

            // تمييز المصطلحات المهمة
            html = html.replace(/\*\*(.*?)\*\*/g, '<strong style="color: #a78bfa;">$1</strong>');
            html = html.replace(/\*(.*?)\*/g, '<em style="color: #c4b5fd;">$1</em>');

            return html;
        }

        function displayIdeas(ideas, topic, isFromHistory = false) {
            const currentTopic = topic || userInput.value.trim();
            if (!isFromHistory) {
                resultsContainer.innerHTML = '';
            }
            if (!ideas || ideas.length === 0) {
                 resultsContainer.innerHTML = '<p class="text-center text-gray-500 p-4 glassmorphism rounded-lg">لم يتمكن الذكاء الاصطناعي من توليد أفكار.</p>';
                 return;
            }
            ideas.forEach((idea, index) => {
                const ideaCard = document.createElement('div');
                ideaCard.className = 'idea-card glassmorphism rounded-xl p-4 md:p-5 animate-slide-in relative mb-4';
                ideaCard.style.animationDelay = `${index * 0.05}s`;

                // تحديد الألوان حسب القسم - مع التحقق من وجود currentCategory
                let titleGradient = 'from-purple-300 to-indigo-300';
                let buttonColor = 'text-purple-300 hover:text-purple-100';
                let iconEmoji = '💡';

                const categoryKey = currentCategory ? currentCategory.key : Object.keys(categoryConfig)[0];

                if (categoryKey === 'health') {
                    titleGradient = 'from-green-300 to-emerald-300';
                    buttonColor = 'text-green-300 hover:text-green-100';
                    iconEmoji = '🏥';
                } else if (categoryKey === 'kitchen') {
                    titleGradient = 'from-orange-300 to-red-300';
                    buttonColor = 'text-orange-300 hover:text-orange-100';
                    iconEmoji = '🍳';
                } else if (categoryKey === 'project') {
                    titleGradient = 'from-blue-300 to-cyan-300';
                    buttonColor = 'text-blue-300 hover:text-blue-100';
                    iconEmoji = '💼';
                } else if (categoryKey === 'educational') {
                    titleGradient = 'from-yellow-300 to-amber-300';
                    buttonColor = 'text-yellow-300 hover:text-yellow-100';
                    iconEmoji = '🎓';
                } else if (categoryKey === 'story') {
                    titleGradient = 'from-pink-300 to-rose-300';
                    buttonColor = 'text-pink-300 hover:text-pink-100';
                    iconEmoji = '📖';
                } else if (categoryKey === 'dealings') {
                    titleGradient = 'from-purple-300 to-violet-300';
                    buttonColor = 'text-purple-300 hover:text-purple-100';
                    iconEmoji = '🤝';
                }

                ideaCard.innerHTML = `
                    <button class="copy-btn p-2" title="نسخ الفكرة" onclick="copyToClipboard(this, this.closest('.idea-card').querySelector('.card-body').innerText)" style="position: absolute; top: 1rem; left: 1rem;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1v-1z"/><path d="M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5-.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3zM-1 3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v11a1 1 0 0 1-1 1H-1a1 1 0 0 1-1-1V3z"/></svg>
                    </button>
                    <div class="flex items-center gap-2 mb-3">
                        <span class="text-2xl">${iconEmoji}</span>
                        <h3 class="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r ${titleGradient}">${idea.title}</h3>
                    </div>
                    <div class="card-body">
                        <div class="bg-gray-800/30 p-3 rounded-lg border-r-2 border-gray-600/50">
                            ${categoryKey === 'educational' ?
                                `<div class="educational-content text-sm">${formatEducationalHTML(idea.description)}</div>` :
                                categoryKey === 'health' ?
                                `<div class="health-content text-sm">${formatHealthHTML(idea.description)}</div>` :
                                categoryKey === 'kitchen' ?
                                `<div class="recipe-content text-sm">${formatRecipeHTML(idea.description)}</div>` :
                                categoryKey === 'project' ?
                                `<div class="project-content text-sm">${formatProjectHTML(idea.description)}</div>` :
                                categoryKey === 'dealings' ?
                                `<div class="dealings-content text-sm">${formatDealingsHTML(idea.description)}</div>` :
                                `<p class="text-gray-300 leading-relaxed text-sm break-words whitespace-pre-wrap">${idea.description}</p>`
                            }
                        </div>
                    </div>
                    <div class="mt-4 pt-4 border-t border-gray-700/50">
                        <button class="text-sm font-semibold ${buttonColor} flex items-center gap-2 hover:gap-3 transition-all duration-300"
                                data-click-count="0"
                                onclick="expandIdea(this, '${currentTopic.replace(/'/g, "\\'")}')">
                            <span>${iconEmoji}</span>
                            <span>${currentCategory ? currentCategory.expandButtonText : 'عرض التفاصيل'}</span>
                            <span>&raquo;</span>
                        </button>
                    </div>
                `;
                resultsContainer.appendChild(ideaCard);
            });
        }

        // --- GEMINI & FIREBASE HELPERS & HISTORY ---
        async function setupFirebase() {
            try {
                const firebaseConfig = {
                    apiKey: "AIzaSyA8icSGnn9lL1T4_F__nJUdEdVMc5MT958",
                    authDomain: "creative-mind-1a1ee.firebaseapp.com",
                    projectId: "creative-mind-1a1ee",
                    storageBucket: "creative-mind-1a1ee.appspot.com",
                    messagingSenderId: "************",
                    appId: "1:************:web:c7b4e01c68a7faca05e60c",
                    measurementId: "G-QC0YEY64GK"
                };

                console.log("Initializing Firebase...");
                const app = initializeApp(firebaseConfig);
                window.db = getFirestore(app);
                window.auth = getAuth(app);

                onAuthStateChanged(window.auth, async (user) => {
                    if (user) {
                        window.userId = user.uid;
                        console.log("User authenticated:", user.uid);
                        updateHistoryStatus(true);
                    } else {
                        console.log("Signing in anonymously...");
                        try {
                            const result = await signInAnonymously(window.auth);
                            window.userId = result.user.uid;
                            console.log("Anonymous sign-in successful:", result.user.uid);
                            updateHistoryStatus(true);
                        } catch (authError) {
                            console.error("Anonymous sign-in failed:", authError);
                            updateHistoryStatus(false);
                        }
                    }
                });
            } catch (error) {
                console.error("Firebase Init Error:", error);
                // Fallback to localStorage if Firebase fails
                window.useLocalStorage = true;
                updateHistoryStatus(false);
            }
        }

        function updateHistoryStatus(isOnline) {
            const statusElement = document.getElementById('historyStatus');
            const modeIndicator = document.getElementById('historyModeIndicator');

            if (statusElement) {
                if (isOnline && window.db && window.userId) {
                    statusElement.className = 'absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full opacity-75';
                    statusElement.title = 'السجل متصل (Firebase)';
                    if (modeIndicator) modeIndicator.textContent = '🌐 متصل بالسحابة - يتم حفظ السجل عبر الإنترنت';
                } else {
                    statusElement.className = 'absolute -top-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full opacity-75';
                    statusElement.title = 'السجل محلي (localStorage)';
                    if (modeIndicator) modeIndicator.textContent = '💾 وضع محلي - يتم حفظ السجل على جهازك فقط';
                }
            }
        }

        async function callGemini(prompt, generationConfig) {
            const payload = { contents: [{ role: "user", parts: [{ text: prompt }] }], ...(generationConfig && { generationConfig }) };
            const apiKey = "AIzaSyA8icSGnn9lL1T4_F__nJUdEdVMc5MT958"; // Your API Key
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
            const response = await fetch(apiUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
            if (!response.ok) throw new Error(`API request failed with status ${response.status}`);
            const result = await response.json();
            if (result.candidates?.[0]?.content?.parts?.[0]?.text) {
                const text = result.candidates[0].content.parts[0].text;
                return generationConfig?.responseMimeType === 'application/json' ? JSON.parse(text) : text;
            } else { throw new Error("لم يتم العثور على محتوى في استجابة الـ API."); }
        }

        async function saveToHistory(topic, style, ideas) {
            try {
                // Try Firebase first
                if (window.db && window.userId && !window.useLocalStorage) {
                    const historyCollection = collection(db, `artifacts/${appId}/users/${userId}/creative_history`);
                    await addDoc(historyCollection, {
                        topic,
                        style,
                        ideas: JSON.stringify(ideas),
                        createdAt: serverTimestamp()
                    });
                    console.log("Saved to Firebase successfully");
                } else {
                    // Fallback to localStorage with auto-cleanup
                    const historyKey = 'creative_mind_history';
                    const existingHistory = JSON.parse(localStorage.getItem(historyKey) || '[]');

                    // تنظيف السجلات القديمة (أكثر من 7 أيام)
                    const sevenDaysAgo = new Date();
                    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

                    const cleanedHistory = existingHistory.filter(entry => {
                        const entryDate = new Date(entry.createdAt);
                        // احتفظ بالسجلات الحديثة فقط (آخر 7 أيام)
                        return entryDate > sevenDaysAgo;
                    });

                    const newEntry = {
                        id: Date.now().toString(),
                        topic,
                        style,
                        ideas: JSON.stringify(ideas),
                        createdAt: new Date().toISOString()
                    };
                    cleanedHistory.unshift(newEntry);

                    // Keep only last 10 entries
                    if (cleanedHistory.length > 10) {
                        cleanedHistory.splice(10);
                    }

                    localStorage.setItem(historyKey, JSON.stringify(cleanedHistory));
                    console.log("Saved to localStorage successfully with cleanup");
                }
            } catch (error) {
                console.error("Error saving to history:", error);
                // Try localStorage as final fallback
                try {
                    const historyKey = 'creative_mind_history';
                    const existingHistory = JSON.parse(localStorage.getItem(historyKey) || '[]');
                    const newEntry = {
                        id: Date.now().toString(),
                        topic,
                        style,
                        ideas: JSON.stringify(ideas),
                        createdAt: new Date().toISOString()
                    };
                    existingHistory.unshift(newEntry);
                    localStorage.setItem(historyKey, JSON.stringify(existingHistory));
                    console.log("Saved to localStorage as fallback");
                } catch (localError) {
                    console.error("Failed to save to localStorage:", localError);
                }
            }
        }

        window.toggleHistoryLog = () => {
            historyLogModal.classList.toggle('active');
            if (historyLogModal.classList.contains('active')) {
                updateHistoryStatus(window.db && window.userId && !window.useLocalStorage);
                loadHistory();
            }
        };


        window.closeHistoryOnOverlay = (event) => { if (event.target === historyLogModal) toggleHistoryLog(); };
        
        window.clearHistory = () => {
             historyLogContent.innerHTML = `
                <div class="text-center p-4">
                    <p class="mb-4">هل أنت متأكد من أنك تريد حذف السجل بالكامل؟ لا يمكن التراجع عن هذا الإجراء.</p>
                    <button onclick="confirmClearHistory()" class="bg-red-600 text-white px-4 py-2 rounded-lg mr-2 hover:bg-red-700">نعم، احذف</button>
                    <button onclick="loadHistory()" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700">إلغاء</button>
                </div>
            `;
        }

        window.confirmClearHistory = async () => {
            historyLogContent.innerHTML = '<div class="custom-loader mx-auto"></div><p class="text-center mt-2">جاري الحذف...</p>';

            try {
                // Clear Firebase if available
                if (window.db && window.userId && !window.useLocalStorage) {
                    const historyCollection = collection(db, `artifacts/${appId}/users/${userId}/creative_history`);
                    const querySnapshot = await getDocs(historyCollection);
                    const deletePromises = [];
                    querySnapshot.forEach((docRef) => {
                        deletePromises.push(deleteDoc(docRef.ref));
                    });
                    await Promise.all(deletePromises);
                }

                // Clear localStorage
                localStorage.removeItem('creative_mind_history');

                historyLogContent.innerHTML = '<p class="text-center text-green-400">تم حذف السجل بنجاح.</p>';
                setTimeout(loadHistory, 1500);
            } catch (error) {
                console.error("Error clearing history:", error);
                // Try to clear localStorage at least
                localStorage.removeItem('creative_mind_history');
                historyLogContent.innerHTML = '<p class="text-center text-green-400">تم حذف السجل المحلي بنجاح.</p>';
                setTimeout(loadHistory, 1500);
            }
        }

        function loadHistory() {
            historyLogContent.innerHTML = '<div class="custom-loader mx-auto"></div>';

            try {
                // Try Firebase first
                if (window.db && window.userId && !window.useLocalStorage) {
                    const q = query(collection(db, `artifacts/${appId}/users/${userId}/creative_history`), orderBy("createdAt", "desc"), limit(10));
                    onSnapshot(q, (snapshot) => {
                        if (snapshot.empty) {
                            loadFromLocalStorage(); // Fallback to localStorage
                            return;
                        }
                        historyLogContent.innerHTML = snapshot.docs.map(doc => {
                            const item = doc.data();
                            const date = item.createdAt ? item.createdAt.toDate().toLocaleString('ar-EG') : 'غير معروف';
                            return createHistoryItemHTML(item.topic, item.style, item.ideas, date);
                        }).join('');
                    }, (error) => {
                        console.error("Firebase error:", error);
                        loadFromLocalStorage(); // Fallback to localStorage
                    });
                } else {
                    loadFromLocalStorage();
                }
            } catch (error) {
                console.error("Error loading history:", error);
                loadFromLocalStorage();
            }
        }

        function loadFromLocalStorage() {
            try {
                const historyKey = 'creative_mind_history';
                let history = JSON.parse(localStorage.getItem(historyKey) || '[]');

                // تنظيف السجلات القديمة عند التحميل
                const sevenDaysAgo = new Date();
                sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

                const cleanedHistory = history.filter(item => {
                    const itemDate = new Date(item.createdAt);
                    // احتفظ بالسجلات الحديثة فقط (آخر 7 أيام)
                    return itemDate > sevenDaysAgo;
                });

                // حفظ السجل المنظف
                if (cleanedHistory.length !== history.length) {
                    localStorage.setItem(historyKey, JSON.stringify(cleanedHistory));
                    history = cleanedHistory;
                }

                if (history.length === 0) {
                    historyLogContent.innerHTML = '<p class="text-center text-gray-400">لا يوجد إلهام محفوظ بعد.</p>';
                    return;
                }

                // عرض أول 10 سجلات فقط
                const limitedHistory = history.slice(0, 10);
                historyLogContent.innerHTML = limitedHistory.map(item => {
                    const date = new Date(item.createdAt).toLocaleString('ar-EG');
                    return createHistoryItemHTML(item.topic, item.style, item.ideas, date);
                }).join('');

                console.log("Loaded from localStorage successfully with cleanup");
            } catch (error) {
                console.error("Error loading from localStorage:", error);
                historyLogContent.innerHTML = '<p class="text-red-400">فشل تحميل السجل</p>';
            }
        }

        function createHistoryItemHTML(topic, style, ideas, date) {
            return `<div class="p-4 rounded-lg bg-gray-800/50 hover:bg-gray-700/50 cursor-pointer transition-all duration-300"
                         data-topic="${topic.replace(/"/g, '&quot;')}"
                         data-style="${style}"
                         data-ideas='${ideas}'
                         onclick='loadFromHistory(this)'>
                         <div class="font-bold">${topic} <span class="text-xs font-normal text-purple-300 bg-purple-900/50 px-2 py-0.5 rounded-full">${style}</span></div>
                         <div class="text-xs text-gray-400 mt-1">${date}</div>
                   </div>`;
        }
        
        window.loadFromHistory = (element) => {
            try {
                const topic = element.dataset.topic;
                const style = element.dataset.style;
                const ideas = JSON.parse(element.dataset.ideas);

                // Find the matching category key
                let categoryKey = Object.keys(categoryConfig).find(key => categoryConfig[key].name === style);

                // إذا لم يتم العثور على القسم (مثل الإبداع المحذوف)، استخدم القسم الأول المتاح
                if (!categoryKey) {
                    categoryKey = Object.keys(categoryConfig)[0];
                    console.log(`Category "${style}" not found, using default: ${categoryKey}`);
                }

                if (categoryKey) {
                    selectCategory(categoryKey);
                    userInput.value = topic;

                    // انتظار قصير للتأكد من تحديث الواجهة
                    setTimeout(() => {
                        resultsContainer.innerHTML = '';
                        if (placeholder) placeholder.style.display = 'none';
                        displayIdeas(ideas, topic, true);
                    }, 100);
                }

                // إغلاق السجل بعد تحميل البيانات
                setTimeout(() => {
                    toggleHistoryLog();
                }, 200);
            } catch (error) {
                console.error('Error loading from history:', error);
                alert('حدث خطأ أثناء استرجاع العنصر من السجل');
            }
        }

        window.copyToClipboard = (button, textToCopy) => {
            const tempTextArea = document.createElement('textarea');
            tempTextArea.value = textToCopy;
            document.body.appendChild(tempTextArea);
            tempTextArea.select();
            document.execCommand('copy');
            document.body.removeChild(tempTextArea);
            
            const originalIcon = button.innerHTML;
            button.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="text-green-400" viewBox="0 0 16 16"><path d="M12.736 3.97a.733.733 0 0 1 1.047 0c.286.289.29.756.01 1.05L7.88 12.01a.733.733 0 0 1-1.065.02L3.217 8.384a.757.757 0 0 1 0-1.06.733.733 0 0 1 1.047 0l3.052 3.093 5.4-6.425a.247.247 0 0 1 .02-.022Z"/></svg>`;
            setTimeout(() => { button.innerHTML = originalIcon; }, 1500);
        };
    </script>
    
    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
    <script>
        const isMobile = window.innerWidth < 600;
        particlesJS('particles-js', {
            "particles": {
                "number": {
                    "value": isMobile ? 20 : 30,
                    "density": {
                        "enable": true,
                        "value_area": isMobile ? 1000 : 1200
                    }
                },                "color": {"value": "#9B4DCA"},
                "shape": {"type": "circle"},
                "opacity": {
                    "value": isMobile ? 0.7 : 0.8,
                    "random": true,
                    "anim": {
                        "enable": true,
                        "speed": isMobile ? 1 : 1.2,
                        "opacity_min": 0.4,
                        "sync": false
                    }
                },                "size": {
                    "value": isMobile ? 2.8 : 3,
                    "random": true,
                    "anim": {
                        "enable": true,
                        "speed": 1.5,
                        "size_min": 0.8,
                        "sync": false
                    }
                },                "line_linked": {
                    "enable": true,
                    "distance": isMobile ? 140 : 150,
                    "color": "#c039ff",
                    "opacity": isMobile ? 0.5 : 0.6,
                    "width": isMobile ? 1.2 : 1.5
                },                "move": {
                    "enable": true,
                    "speed": isMobile ? 0.8 : 1.2,
                    "direction": "none",
                    "random": true,
                    "straight": false,
                    "out_mode": "out",
                    "bounce": false,
                    "attract": {
                        "enable": false
                    }
                },
                "interactivity": {
                    "detect_on": "canvas",
                    "events": {
                        "onhover": {
                            "enable": isMobile ? false : true,
                            "mode": "grab"
                        },
                        "onclick": {
                            "enable": false
                        },
                        "resize": true
                    },
                    "modes": {
                        "grab": {
                            "distance": 100,
                            "line_linked": {
                                "opacity": 0.3
                            }
                        }
                    }
                }
            },
            "interactivity": {
                "detect_on": "canvas",
                "events": {
                    "onhover": {
                        "enable": true,
                        "mode": "grab"
                    },
                    "onclick": {
                        "enable": true,
                        "mode": "push"
                    },
                    "resize": true
                },
                "modes": {
                    "grab": {
                        "distance": isMobile ? 100 : 140,
                        "line_linked": {"opacity": isMobile ? 0.5 : 1}
                    },
                    "push": {"particles_nb": isMobile ? 2 : 4}
                }
            },
            "retina_detect": true
        });
    </script>
</body>
</html>